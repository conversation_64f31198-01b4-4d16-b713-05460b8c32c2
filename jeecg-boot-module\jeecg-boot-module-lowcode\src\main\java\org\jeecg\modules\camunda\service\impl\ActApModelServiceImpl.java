package org.jeecg.modules.camunda.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.camunda.entity.ActApModel;
import org.jeecg.modules.camunda.mapper.ActApModelMapper;
import org.jeecg.modules.camunda.service.ActApModelService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.Random;

/**
 * <AUTHOR>
 */
@Service
@Primary
@Slf4j
public class ActApModelServiceImpl extends ServiceImpl<ActApModelMapper, ActApModel>
        implements ActApModelService {

    /**
     * 初始化流程文件xml
     *
     * @param model
     */
    @Override
    public String initXml(ActApModel model) {
        String uuid = getRandomStr(7);
        String xmlStr = """
                <?xml version="1.0" encoding="UTF-8"?>
                <bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_%s" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.36.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.23.0">
                  <bpmn:process id="%s" name="%s" isExecutable="true" camunda:historyTimeToLive="365" />
                  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
                    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="%s" />
                  </bpmndi:BPMNDiagram>
                </bpmn:definitions>
                """.formatted(uuid,model.getModelKey(),model.getModelName(),model.getModelKey());
        return xmlStr;
    }

    /**
     * 获取指定位数的随机字符串
     *
     * @param num
     * @return
     */
    private String getRandomStr(int num) {
        //先定义取值范围
        String chars = "0123456789abcdefghijklmnopqrstuvwxyz";
        StringBuffer value = new StringBuffer();
        for (int i = 0; i < num; i++) {
            Random random = new Random();
            int randomNumber = random.nextInt(36);
            value.append(chars.charAt(randomNumber));
        }
        return value.toString();
    }
}




