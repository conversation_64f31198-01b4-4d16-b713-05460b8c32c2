package org.jeecg.modules.camunda.emuns;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */

public enum CustomType {
    /**
     * TASK_CUS_USERS:节点用户
     * PROC_CUS_COPY:抄送
     * CAND_RULE:候选人规则
     * PROC_TABLE:数据表
     * TASK_CUS_BTNS:按钮权限
     * TASK_CUS_VARS:表单参数
     */
    TASK_CUS_USERS, PROC_CUS_COPY, CAND_RULE, PROC_TABLE, TASK_CUS_BTNS, TASK_CUS_VARS;
    private static final LinkedHashMap<CustomType, String> NAME_SPACE = new LinkedHashMap<CustomType, String>();

    static {
        NAME_SPACE.put(TASK_CUS_USERS, "proc_cus_task_users");
        NAME_SPACE.put(PROC_CUS_COPY, "proc_cus_copy");
        NAME_SPACE.put(CAND_RULE, "proc_cus_cand_rule");
        NAME_SPACE.put(PROC_TABLE, "proc_cus_table");
        NAME_SPACE.put(TASK_CUS_BTNS, "proc_cus_task_btns");
        NAME_SPACE.put(TASK_CUS_VARS, "proc_cus_task_vars");
    }

    public String getName() {
        return NAME_SPACE.get(this);
    }
}
