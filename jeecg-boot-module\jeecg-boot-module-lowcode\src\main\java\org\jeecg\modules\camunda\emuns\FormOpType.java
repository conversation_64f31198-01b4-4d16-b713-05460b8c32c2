package org.jeecg.modules.camunda.emuns;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */

public enum FormOpType {
    /**
     * EDIT:编辑
     * SHOW：暂时
     */
    EDIT, SHOW;
    private static final LinkedHashMap<FormOpType, String> NAME_SPACE = new LinkedHashMap<FormOpType, String>();

    static {
        NAME_SPACE.put(EDIT, "fillFormCode");
        NAME_SPACE.put(SHOW, "readFormCode");
    }

    public String getName() {
        return NAME_SPACE.get(this);
    }
}
