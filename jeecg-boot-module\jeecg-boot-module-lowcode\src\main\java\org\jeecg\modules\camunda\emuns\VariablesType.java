package org.jeecg.modules.camunda.emuns;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */

public enum VariablesType {
    /**
     * 属性类型
     */
    COPY_USERS, START_USER, PROC_TABLE;
    private static final LinkedHashMap<VariablesType, String> NAME_SPACE = new LinkedHashMap<VariablesType, String>();

    static {
        NAME_SPACE.put(COPY_USERS, "copyUsers");
        NAME_SPACE.put(START_USER, "startUser");
        NAME_SPACE.put(PROC_TABLE, "procTable");
    }

    public String getName() {
        return NAME_SPACE.get(this);
    }
}
