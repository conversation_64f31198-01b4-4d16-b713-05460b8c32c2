package org.jeecg.modules.camunda.emuns;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */

public enum ActiveType {
    /**
     * ACTIVE:激活
     * SUSPEND：挂起、暂停
     */
    ACTIVE, SUSPEND;
    private static final LinkedHashMap<ActiveType, String> NAME_SPACE = new LinkedHashMap<ActiveType, String>();

    static {
        NAME_SPACE.put(ACTIVE, "ACTIVE");
        NAME_SPACE.put(SUSPEND, "SUSPEND");
    }

    public String getName() {
        return NAME_SPACE.get(this);
    }
}
