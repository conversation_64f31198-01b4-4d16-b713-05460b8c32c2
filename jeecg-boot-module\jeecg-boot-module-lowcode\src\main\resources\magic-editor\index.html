<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="UTF-8">
	<link rel="icon" href="favicon.png">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>magic-api</title>
	<style>
		.magic-loading-wrapper {position: absolute;top: 0;bottom: 0;left: 0;right: 0;z-index: 10000000;text-align: center;background: #fff;}
		.magic-loading-container {position: absolute;top: 50%;left: 50%;width: 500px;height: 100px;margin-left: -250px;margin-top: -50px;text-align: center;}
		.magic-loading-container .magic-title {font-size: 0;color: #0075ff;letter-spacing: 0;}
		.magic-loading-container .magic-title label {font-size: 14px;display: inline-block;margin-top: 5px;vertical-align: top;}
		.magic-loading-container .magic-title span {font-size: 20px;display: inline-block;padding: 0 3px;animation: stretch 1s infinite;}
		.magic-loading-container .magic-title span:nth-child(1) {animation-delay: calc(1s / 8 * 0 / 2);}
		.magic-loading-container .magic-title span:nth-child(2) {animation-delay: calc(1s / 8 * 1 / 2);}
		.magic-loading-container .magic-title span:nth-child(3) {animation-delay: calc(1s / 8 * 2 / 2);}
		.magic-loading-container .magic-title span:nth-child(4) {animation-delay: calc(1s / 8 * 3 / 2);}
		.magic-loading-container .magic-title span:nth-child(5) {animation-delay: calc(1s / 8 * 4 / 2);}
		.magic-loading-container .magic-title span:nth-child(6) {animation-delay: calc(1s / 8 * 5 / 2);}
		.magic-loading-container .magic-title span:nth-child(7) {animation-delay: calc(1s / 8 * 6 / 2);}
		.magic-loading-container .magic-title span:nth-child(8) {animation-delay: calc(1s / 8 * 7 / 2);}
		.magic-loading-container .magic-loading-text {text-align: center;font-weight: bolder;font-style: italic;color: #889aa4;font-size: 14px;margin-top: 5px;animation: blink-loading 2s ease-in infinite;}
		@keyframes stretch {0% {transform: scale(1);}25% {transform: scale(1.2);}50% {transform: scale(1);}100% {transform: scale(1);}}
		@keyframes blink-loading {0% {opacity: 1;}50% {opacity: 0.5;}100% {opacity: 1;}}
	</style>
  <script type="module" crossorigin src="./assets/index.8dc5e5af.js"></script>
  <link rel="modulepreload" href="./assets/vue.6f28a6f0.js">
  <link rel="modulepreload" href="./assets/axios.23e7b955.js">
  <link rel="modulepreload" href="./assets/vendor.295b3547.js">
  <link rel="modulepreload" href="./assets/app.60f63c60.js">
  <link rel="stylesheet" href="./assets/style.5b6b651e.css">
</head>
<body>
	<div class="magic-loading-wrapper" id="magic-loading-wrapper">
		<div class="magic-loading-container">
			<div class="magic-title">
				<span>L</span>
				<span>o</span>
				<span>a</span>
				<span>d</span>
				<span>i</span>
				<span>n</span>
				<span>g</span>
			</div>
			<div class="magic-loading-text" id="magic-loading-text"></div>
		</div>
	</div>
	<script>
		function showMaLoadingText(){
			let defaultConfig = {
				title: 'magic-api',
				version: '2.2.2'
			}
			defaultConfig = { ...defaultConfig, ...window.MAGIC_EDITOR_CONFIG }
			let $dom = document.getElementById('magic-loading-text')
			$dom.innerText = 'By ' + defaultConfig.title + ' ' + defaultConfig.version
			document.title = defaultConfig.title + ' v' + defaultConfig.version
		}
		function hideMaLoading() {
			document.getElementById('magic-loading-wrapper').style.display = 'none'
		}
	</script>
	<script src="./config-js" onload="showMaLoadingText()" onerror="showMaLoadingText()"></script>
	<div id="app"></div>

</body>
</html>
