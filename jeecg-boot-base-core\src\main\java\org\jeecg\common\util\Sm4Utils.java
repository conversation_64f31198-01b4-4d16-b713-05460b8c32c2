package org.jeecg.common.util;

import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.SM4;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * sm4国标加密工具类
 * <AUTHOR>
 */
public class Sm4Utils {
    /**
     * 转16进制
     */
    private static final String HEX_DIGITS ="abcdefghijklmn0123456789opqrstuvwxyz";

    private static final SM4 sm4 = new SM4(
            Mode.CBC, Padding.PKCS5Padding,
            getKey().getBytes(StandardCharsets.UTF_8),
            getIv().getBytes(StandardCharsets.UTF_8)
    );

    /**
     * 获取初始化向量
     * @return
     */
    public static String getIv() {
        // Define the position array
        int[] pos = {22, 9, 17, 33, 27, 30, 18, 26, 10, 31, 23, 13, 0, 29, 25, 11};
        // Create a list to store the characters
        List<Character> arrayIndex = new ArrayList<>();
        // Iterate over the positions array and populate the list
        for (int val : pos) {
            arrayIndex.add(HEX_DIGITS.charAt(val % HEX_DIGITS.length()));
        }
        // Convert the list to a string
        StringBuilder result = new StringBuilder();
        for (char c : arrayIndex) {
            result.append(c);
        }
        return result.toString();
    }
    /**
     * 获取秘钥
     * @return
     */
    public static String getKey() {
        // Define the position array
        int[] pos = {18, 11, 2, 22, 9, 4, 15, 16, 11, 17, 34, 10, 0, 35, 25, 12};
        // Create a list to store the characters
        List<Character> arrayIndex = new ArrayList<>();
        // Iterate over the positions array and populate the list
        for (int val : pos) {
            arrayIndex.add(HEX_DIGITS.charAt(val % HEX_DIGITS.length()));
        }
        // Convert the list to a string
        StringBuilder result = new StringBuilder();
        for (char c : arrayIndex) {
            result.append(c);
        }
        return result.toString();
    }

    /**
     * 使用默认的key和iv加密
     * @param data
     * @return
     * @throws Exception
     */
    public static String encrypt(String data)  {
        return  sm4.encryptBase64(data);
    }
    /**
     * 使用默认的key和iv解密
     * @param data
     * @return
     * @throws Exception
     */
    public static String desEncrypt(String data) {
        return  sm4.decryptStr(data);
    }
}
