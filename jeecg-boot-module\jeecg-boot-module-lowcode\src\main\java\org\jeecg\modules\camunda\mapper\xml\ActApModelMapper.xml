<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.camunda.mapper.ActApModelMapper">

    <!--待选人员查询-->
    <select id="queryUserPage"
            resultType="org.jeecg.modules.camunda.model.UserModel">
        select u.id "id",
        u.realname "realname",
        u.org_id "orgId",
        d.depart_name "orgName"
        from sys_user u, sys_depart d
        where u.status = 1
        and u.org_id = d.id
        <if test="name != null and name != ''">
            and u.realname like CONCAT(CONCAT('%', #{name}), '%')
        </if>
    </select>
    <!--已选人员查询-->
    <select id="queryUserSelect"
            resultType="org.jeecg.modules.camunda.model.UserModel">
        select u.id "id",
        u.realname "realname",
        u.org_id "orgId",
        d.depart_name "orgName"
        from sys_user u, sys_depart d
        where u.status = 1
        and u.org_id = d.id
        and u.id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
    <!--待选角色查询-->
    <select id="queryRolePage"
            resultType="org.jeecg.modules.camunda.vo.RoleVo">
        select r.id "id", r.role_name "roleName"
        from sys_role r
        <where>
            <if test="name != null and name != ''">
                and r.role_name like CONCAT(CONCAT('%', #{name}), '%')
            </if>
        </where>
        order by r.create_time
    </select>
    <!--已选角色查询-->
    <select id="queryRoleSelect"
            resultType="org.jeecg.modules.camunda.vo.RoleVo">
        select r.id "id", r.role_name "roleName"
        from sys_role r
        where r.id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
    <!--待选职位查询-->
    <select id="queryPositionListPage"
            resultType="org.jeecg.modules.camunda.vo.TaskPositionVo">
        select p.id "id", p.name "name" from sys_position p
        <where>
            <if test="name != null and name != ''">
                and p.name like CONCAT(CONCAT('%', #{name}), '%')
            </if>
        </where>
        order by p.create_time
    </select>
    <!--已选职位查询-->
    <select id="queryPositionSelect"
            resultType="org.jeecg.modules.camunda.vo.TaskPositionVo">
        select p.id "id", p.name "name" from sys_position p
        where p.id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <!--所有业务类型-->
    <select id="queryApType"
            resultType="org.jeecg.modules.camunda.entity.ActApType">
        select *
          from act_ap_type t
         where t.id in (select distinct a.type_id
                          from act_ap_model a, act_ap_model_role r
                         where a.deploy_state = 'DEPLOY'
                           and a.use_state = 'ACTIVE'
                           and a.id = r.model_id
                           and r.role_id in
                               (select role_id
                                  from sys_user_role
                                 where user_id = #{userId}))
    </select>
    <!--所有权限内流程模板-->
    <select id="queryModelByAuth"
            resultType="org.jeecg.modules.camunda.entity.ActApModel">
        select a.*
        from act_ap_model a, act_ap_model_role r
        where a.deploy_state = 'DEPLOY'
        and a.use_state = 'ACTIVE'
        and a.id = r.model_id
        and r.role_id in (select role_id
        from sys_user_role
        where user_id = #{userId})
        <if test="typeId != null and typeId != ''">
            and a.type_id = #{typeId}
        </if>
        order by a.seq
    </select>
</mapper>
