package org.jeecg.modules.camunda.emuns;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */

public enum DeployType {
    /**
     * INIT：初始化
     * DEPLOY：发布
     */
    INIT, DEPLOY;
    private static final LinkedHashMap<DeployType, String> NAME_SPACE = new LinkedHashMap<DeployType, String>();

    static {
        NAME_SPACE.put(INIT, "INIT");
        NAME_SPACE.put(DEPLOY, "DEPLOY");
    }

    public String getName() {
        return NAME_SPACE.get(this);
    }
}
