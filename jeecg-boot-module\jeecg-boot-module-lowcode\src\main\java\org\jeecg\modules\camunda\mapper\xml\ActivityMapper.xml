<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.camunda.mapper.ActivityMapper">
    <!--删除BPMN流程运行时记录-->
    <delete id="deleteRuExecutionById" parameterType="java.lang.String">
        DELETE
        FROM act_ru_execution
        where id_ = #{id}
    </delete>
    <!--删除运行时流程人员表-->
    <delete id="deleteRuIdentitylinkByTaskId" parameterType="java.lang.String">
        DELETE
        FROM act_ru_identitylink a
        where a.task_id_ = #{taskId}
    </delete>
    <!--删除流程运行时变量表-->
    <delete id="deleteRuVariableByExId" parameterType="java.lang.String">
        DELETE
        FROM act_ru_variable a
        where a.execution_id_ = #{exId}
    </delete>
    <!--删除流程运行时任务表-->
    <delete id="deleteRuTaskById" parameterType="java.lang.String">
        DELETE
        FROM act_ru_task
        where id_ = #{id}
    </delete>
    <!--根据单位数组获取用户-->
    <select id="queryUserIdsByDepart" resultType="java.lang.String">
        select distinct u.id
        from sys_user u
        where u.org_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <!--根据角色数组获取用户-->
    <select id="queryUserIdsByRole" resultType="java.lang.String">
        select distinct a.user_id
        from sys_user_role a
        where a.role_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <!--根据职务数组获取用户-->
    <select id="queryUserIdsByPost" resultType="java.lang.String">
        select u.id
        from sys_user u, sys_position p
        where u.post = p.code
        and p.id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <!--根据单位id获取用户-->
    <select id="queryUserIdsByOrgId" resultType="java.lang.String">
        select t.id
        from sys_user t,
             sys_depart d
        where t.org_id = d.id
          and d.id =
              (select u.org_id from sys_user u where u.id = #{id})
    </select>
    <!--根据角色id获取用户-->
    <select id="queryUserIdsByRoleId" resultType="java.lang.String">
        select u.id
        from sys_user u
        where u.id in (select distinct t.user_id
                       from sys_user_role t
                       where t.role_id in
                             (select r.role_id
                              from sys_user_role r
                              where r.user_id = #{id}))
          and u.status = 1
    </select>
    <!--根据角色id获取用户-->
    <select id="queryUserIdsByPositionId" resultType="java.lang.String">
        select u.id
        from sys_user u,
             sys_position p
        where u.post = p.code
          and p.code = #{id}
          and u.status = 1
    </select>

    <!--根据sql语句的修改数据-->
    <insert id="updateBySql" parameterType="String">
        ${sqlStr}
    </insert>
</mapper>