package org.jeecg.modules.camunda.emuns;

import java.util.LinkedHashMap;

/**
 * 流程dom类型枚举
 *
 * <AUTHOR>
 */

public enum DomType {
    /**
     * MULTI:多实例循环任务
     * BODY：多实例体
     */
    MULTI, BODY, USER_TASK, GATEWAY;
    private static final LinkedHashMap<DomType, String> NAME_SPACE = new LinkedHashMap<DomType, String>();

    static {
        NAME_SPACE.put(MULTI, "multiInstanceLoopCharacteristics");
        NAME_SPACE.put(BODY, "multiInstanceBody");
        NAME_SPACE.put(USER_TASK, "userTask");
        NAME_SPACE.put(GATEWAY, "exclusiveGateway");
    }

    public String getName() {
        return NAME_SPACE.get(this);
    }
}
