package org.jeecg.modules.camunda.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.camunda.entity.ActApCopy;
import org.jeecg.modules.camunda.mapper.ActApCopyMapper;
import org.jeecg.modules.camunda.service.ActApCopyService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Primary
@Slf4j
public class ActApCopyServiceImpl extends ServiceImpl<ActApCopyMapper, ActApCopy>
        implements ActApCopyService {

}




