package org.jeecg.modules.camunda.emuns;

import java.util.LinkedHashMap;

/**
 * 候选人规则枚举
 *
 * <AUTHOR>
 */
public enum CandRuleType {
    /**
     * INTERSECTION:交集
     * UNION:并集
     */
    INTERSECTION, UNION;
    private static final LinkedHashMap<CandRuleType, String> NAME_SPACE = new LinkedHashMap<CandRuleType, String>();

    static {
        NAME_SPACE.put(INTERSECTION, "intersection");
        NAME_SPACE.put(UNION, "union");
    }

    public String getName() {
        return NAME_SPACE.get(this);
    }
}
