package org.jeecg.modules.amis.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.amis.entity.DwfType;
import org.jeecg.modules.amis.mapper.DwfTypeMapper;
import org.jeecg.modules.amis.service.DwfTypeService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 表单业务类型
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/dwf/type")
public class DwfTypeController {

    @Resource
    private DwfTypeService dwfTypeService;
    @Resource
    private DwfTypeMapper dwfTypeMapper;

    /**
     * 分页列表查询
     *
     * @param dwfType
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "动态表单类型管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> list(DwfType dwfType, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                          HttpServletRequest req) {
        QueryWrapper<DwfType> queryWrapper = QueryGenerator.initQueryWrapper(dwfType, req.getParameterMap());
        queryWrapper.orderByAsc("seq");
        Page<DwfType> page = new Page<DwfType>(pageNo, pageSize);
        IPage<DwfType> pageList = dwfTypeService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 查询所有
     *
     * @param dwfType
     * @return
     */
    @AutoLog(value = "动态表单类型管理-查询所有")
    @GetMapping(value = "/listAll")
    public Result<?> list(DwfType dwfType) {
        QueryWrapper<DwfType> queryWrapper = new QueryWrapper<DwfType>();
        queryWrapper.orderByAsc("seq");
        List<DwfType> list = dwfTypeMapper.selectList(queryWrapper);
        return Result.OK(list);
    }

    /**
     * 添加
     *
     * @param dwfType
     * @return
     */
    @AutoLog(value = "动态表单类型管理-添加")
    @PostMapping(value = "/saveOrUpdate")
    public Result<?> saveOrUpdate(@RequestBody DwfType dwfType) {
        dwfTypeService.saveOrUpdate(dwfType);
        return Result.OK(dwfType);
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        DwfType dwfType = dwfTypeService.getById(id);
        return Result.OK(dwfType);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "动态表单类型管理-通过id删除")
    @DeleteMapping(value = "/delete")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        dwfTypeService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 根据名称查询分页
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @GetMapping(value = "/listByName")
    public Result<?> listByName(@RequestParam(name = "typeName", required = false) String typeName,
                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                HttpServletRequest req) {
        QueryWrapper<DwfType> queryWrapper = new QueryWrapper<DwfType>();
        if (StringUtils.isNotEmpty(typeName)) {
            queryWrapper.like("type_name", typeName);
        }
        queryWrapper.orderByAsc("seq");
        Page<DwfType> page = new Page<DwfType>(pageNo, pageSize);
        IPage<DwfType> pageList = dwfTypeService.page(page, queryWrapper);
        return Result.OK(pageList);
    }
}
