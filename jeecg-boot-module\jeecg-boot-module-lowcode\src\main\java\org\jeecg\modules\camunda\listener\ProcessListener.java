package org.jeecg.modules.camunda.listener;

import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.impl.history.event.HistoryEvent;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.EndEvent;
import org.camunda.bpm.spring.boot.starter.event.ExecutionEvent;
import org.camunda.bpm.spring.boot.starter.event.TaskEvent;
import org.jeecg.modules.camunda.emuns.VariablesType;
import org.jeecg.modules.camunda.entity.ActApCopy;
import org.jeecg.modules.camunda.mapper.ActivityMapper;
import org.jeecg.modules.camunda.service.ActApCopyService;
import org.jeecg.modules.camunda.vo.TableVo;
import org.jeecg.modules.camunda.vo.TaskUserVo;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ProcessListener {
    @Resource
    private RuntimeService runtimeService;
    @Resource
    private RepositoryService repositoryService;
    @Resource
    private ActApCopyService actApCopyService;
    @Resource
    private ActivityMapper activityMapper;
    public static final String EVENTNAME_START = "start";
    public static final String EVENTNAME_END = "end";
    public static final String EVENTNAME_TAKE = "take";

    @EventListener
    public void onTaskEvent(DelegateTask taskDelegate) {
        //处理可变任务事件
    }

    @EventListener
    public void onTaskEvent(TaskEvent taskEvent) {
        //处理不可变任务事件
    }

    @EventListener
    public void onExecutionEvent(DelegateExecution executionDelegate) {
        //进入整体流程结束事件处理
        if (executionDelegate.getEventName().equals(EVENTNAME_START)) {
            BpmnModelInstance bpmnModelInstance = executionDelegate.getBpmnModelInstance();
            Collection<EndEvent> endEventList = bpmnModelInstance.getModelElementsByType(EndEvent.class);
            for (EndEvent endEvent : endEventList) {
                if (endEvent.getId().equals(executionDelegate.getCurrentActivityId())) {
                    //业务处理
                    Map<String, Object> variables = runtimeService.getVariables(executionDelegate.getProcessInstanceId());
                    String jsonTableStr = (String) variables.get(VariablesType.PROC_TABLE.getName());
                    TableVo tableVo = JSON.parseObject(jsonTableStr, TableVo.class);
                    if (tableVo != null) {
                        //状态值为F 完成
                        String updateSql = "update " + tableVo.getTableName() + " set " + tableVo.getStateCode() + " ='F' where " + tableVo.getTableKey() + " ='" + executionDelegate.getBusinessKey() + "'";
                        activityMapper.updateBySql(updateSql);
                    }
                    //如果开启抄送
                    if (variables.containsKey(VariablesType.COPY_USERS.getName())) {
                        // 获取流程定义
                        ProcessDefinition processDefinition = repositoryService.getProcessDefinition(executionDelegate.getProcessDefinitionId());
                        List<String> copyUsers = (List<String>) variables.get(VariablesType.COPY_USERS.getName());
                        TaskUserVo user = JSON.parseObject(String.valueOf(variables.get(VariablesType.START_USER.getName())), TaskUserVo.class);
                        for (String userId : copyUsers) {
                            ActApCopy copyData = new ActApCopy();
                            copyData.setRecvUserId(userId);
                            copyData.setBusinessKey(executionDelegate.getBusinessKey());
                            copyData.setProcKey(processDefinition.getKey());
                            copyData.setProcName(processDefinition.getName());
                            copyData.setStartUserId(user.getId());
                            copyData.setStartUser(user.getRealName());
                            copyData.setProcInstId(executionDelegate.getProcessInstanceId());
                            //新建抄送人信息
                            actApCopyService.save(copyData);
                        }
                    }
                }
            }
        }
    }

    @EventListener
    public void onExecutionEvent(ExecutionEvent executionEvent) {
        //处理不可变执行事件
    }

    @EventListener
    public void onHistoryEvent(HistoryEvent historyEvent) {
        //处理历史事件
    }

}
