package org.jeecg.modules.camunda.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ActRuTask implements Serializable {
    /**
     * 业务主键
     */
    private String businessKey;
    /**
     * 流程key
     */
    private String procDefKey;
    /**
     * 流程名称
     */
    private String procDefName;
    /**
     * 流程起始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date procStartTime;
    /**
     * 发起人id
     */
    private String startUserId;
    /**
     * 发起人
     */
    private String startUserName;

    private String id;
    private Long rev;
    private String executionId;
    private String procInstId;
    private String procDefId;
    private String caseExecutionId;
    private String caseInstId;
    private String caseDefId;
    private String name;
    private String parentTaskId;
    private String description;
    private String taskDefKey;
    private String owner;
    private String assignee;
    private String delegation;
    private Long priority;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdated;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dueDate;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date followUpDate;
    private Long suspensionState;
    private String tenantId;
    private static final long serialVersionUID = 1L;
}