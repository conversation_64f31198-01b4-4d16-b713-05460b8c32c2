package org.jeecg.modules.camunda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.jeecg.modules.camunda.entity.ActApOpLog;
import org.jeecg.modules.camunda.model.ActHiProcinst;
import org.jeecg.modules.camunda.model.ActRuTask;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @Entity camunda.entity.ActApOpLog
 */
@Mapper
@Repository
public interface ActApOpLogMapper extends BaseMapper<ActApOpLog> {

    /**
     * 我的发起
     *
     * @param page
     * @param procinst
     * @return
     */
    IPage<ActHiProcinst> queryHisProcinst(IPage<ActHiProcinst> page, ActHiProcinst procinst);

    /**
     * 我的待办
     *
     * @param page
     * @param runTask
     * @return
     */
    IPage<ActRuTask> queryRunTask(IPage<ActRuTask> page, ActRuTask runTask);

    /**
     * 我的经办
     *
     * @param page
     * @param procinst
     * @return
     */
    IPage<ActHiProcinst> queryHandleProcinst(IPage<ActHiProcinst> page, ActHiProcinst procinst);

    /**
     * 抄送我的
     *
     * @param page
     * @param procinst
     * @return
     */
    IPage<ActHiProcinst> querySendProcinst(IPage<ActHiProcinst> page, ActHiProcinst procinst);

}




