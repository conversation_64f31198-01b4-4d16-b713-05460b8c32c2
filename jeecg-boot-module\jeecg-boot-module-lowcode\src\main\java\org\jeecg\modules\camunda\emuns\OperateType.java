package org.jeecg.modules.camunda.emuns;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */

public enum OperateType {
    /**
     * SUBMIT:提交
     * COMPLETE:通过
     * TRANSFER:转办
     * ADD:加签
     * REDUCE:减签
     * JUMP:跳转
     * REJECT:驳回
     * RECALL:撤回至起点
     */
    SUBMIT, COMPLETE, TRANSFER, ADD, REDUCE, JUMP, REJECT, RECALL;
    private static final LinkedHashMap<OperateType, String> NAME_SPACE = new LinkedHashMap<OperateType, String>();

    static {
        NAME_SPACE.put(SUBMIT, "submit");
        NAME_SPACE.put(COMPLETE, "complete");
        NAME_SPACE.put(TRANSFER, "transfer");
        NAME_SPACE.put(ADD, "add");
        NAME_SPACE.put(REDUCE, "reduce");
        NAME_SPACE.put(JUM<PERSON>, "jump");
        NAME_SPACE.put(REJECT, "reject");
        NAME_SPACE.put(RECALL, "recall");

    }

    public String getName() {
        return NAME_SPACE.get(this);
    }

}
