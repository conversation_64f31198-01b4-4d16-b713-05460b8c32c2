package org.jeecg.modules.camunda.util.pojo;

import org.dom4j.Node;

import java.util.List;

/**
 * <AUTHOR>
 */
public class XmlElement {
    private Node node;
    /**
     * 当前结点的所有子节点
     */
    private List<XmlElement> children;
    /**
     * 当前结点是否有连接符
     */
    private Boolean isPrepend;
    /**
     * 节点是否有效
     */
    private Boolean isValid;

    public Node getNode() {
        return node;
    }

    public void setNode(Node node) {
        this.node = node;
    }

    public List<XmlElement> getChildren() {
        return children;
    }

    public void setChildren(List<XmlElement> children) {
        this.children = children;
    }

    public Boolean getIsPrepend() {
        return isPrepend;
    }

    public void setIsPrepend(Boolean isPrepend) {
        this.isPrepend = isPrepend;
    }

    public Boolean getIsValid() {
        return isValid;
    }

    public void setIsValid(Boolean isValid) {
        this.isValid = isValid;
    }

}
