package org.jeecg.modules.camunda.service;

import com.alibaba.fastjson.JSONObject;
import org.camunda.bpm.engine.runtime.ProcessInstanceModificationBuilder;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.UserTask;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.camunda.model.TaskModel;
import org.jeecg.modules.camunda.vo.ApiVo;
import org.jeecg.modules.camunda.vo.OpLogVo;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ActivityService {
    /**
     * 流程启动并直接提交申请
     *
     * @param apiVo
     * @return
     * @throws IOException
     */
    Result<JSONObject> startAndComplete(ApiVo apiVo) throws IOException;

    /**
     * 完成待办
     *
     * @param apiVo
     */
    void complete(ApiVo apiVo);

    /**
     * 外部任务完成待办
     *
     * @param apiVo
     */
    void externalComplete(ApiVo apiVo);

    /**
     * 流程实例启动
     *
     * @param processKey
     * @param businessKey
     * @param result
     * @return
     * @throws IOException
     */
    Result<JSONObject> startProcessInstanceByKey(String processKey, String businessKey, Result<JSONObject> result) throws IOException;

    /**
     * 删除任务
     *
     * @param targetTask
     */
    void deleteTask(Task targetTask);

    /**
     * 流程操作日志添加
     *
     * @param processInstanceModificationBuilder
     * @param opLogVo
     */
    void addOpLog(ProcessInstanceModificationBuilder processInstanceModificationBuilder, OpLogVo opLogVo);

    /**
     * 获取任务节点配置信息
     *
     * @param procInsId
     * @param taskKey
     * @return
     */
    UserTask getUerTask(String procInsId, String taskKey);

    /**
     * 判断任务节点是不是第一个任务节点
     *
     * @param procInsId
     * @param taskKey
     * @return
     */
    boolean isFirstTask(String procInsId, String taskKey);

    /**
     * 获取节点
     *
     * @param procInsId
     * @param taskKey
     * @return
     * @throws IOException
     */
    FlowNode getFlowNode(String procInsId, String taskKey) throws IOException;

    /**
     * 根据实例获取任务节点状态
     *
     * @param procInsId
     * @return
     */
    List<TaskModel> getTaskListByProcInst(String procInsId);

    /**
     * 查询某节点之前所有节点
     *
     * @param node
     * @param predecessors
     * @param visitedNodes
     */
    void findPreFlowList(FlowNode node, List<FlowNode> predecessors, List<FlowNode> visitedNodes);

    /**
     * 获取下一个节点key
     *
     * @param procInsId
     * @param currTaskKey
     * @return
     */
    String getNextFlowNode(String procInsId, String currTaskKey);
}
