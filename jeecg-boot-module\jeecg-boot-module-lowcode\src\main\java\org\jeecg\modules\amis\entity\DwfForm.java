package org.jeecg.modules.amis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 业务表单
 *
 * <AUTHOR>
 * @TableName dwf_form
 */
@TableName(value = "dwf_form")
@Data
public class DwfForm implements Serializable {
    /**
     * id
     */
    @TableId
    private String id;

    /**
     * 表单编码
     */
    private String formCode;

    /**
     * 表单名称
     */
    private String formName;

    /**
     * 表单设计
     */
    private String jsonSchema;

    /**
     * 表单类型id
     */
    private String typeId;

    /**
     * 表单类型名称
     */
    private String typeName;

    /**
     * 业务表名
     */
    private String tableName;

    /**
     * 业务主键
     */
    private String tableKey;

    /**
     * 状态字段
     */
    private String stateCode;

    /**
     * 流程模型key
     */
    private String modelKey;

    /**
     * 流程名称
     */
    private String modelName;

    /**
     * 表单图标
     */
    private String icon;

    /**
     * 序号
     */
    private Integer seq;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}