package org.jeecg.modules.camunda.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.camunda.entity.ActApOpLog;
import org.jeecg.modules.camunda.mapper.ActApOpLogMapper;
import org.jeecg.modules.camunda.service.ActApOpLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
@Primary
@Slf4j
public class ActApOpLogServiceImpl extends ServiceImpl<ActApOpLogMapper, ActApOpLog>
        implements ActApOpLogService {
    @Autowired
    private RuntimeService runtimeService;

    /**
     * 添加操作日期
     *
     * @param opLog
     */
    public ActApOpLog addOpLog(ActApOpLog opLog) {
        LoginUser sysUser = this.getLoginUser();
        opLog.setOpUserId(sysUser.getId());
        opLog.setOpUser(sysUser.getRealname());
        //获取实例
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(opLog.getProcInstId())
                .singleResult();
        opLog.setBusinessKey(processInstance.getBusinessKey());
        this.save(opLog);
        return opLog;
    }

    /**
     * 获取当前登录用户
     */
    public LoginUser getLoginUser() {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser == null) {
            sysUser = new LoginUser();
            sysUser.setId("1509430551733776386");
            sysUser.setRealname("胡伟楠");
        }
        return sysUser;
    }
}




