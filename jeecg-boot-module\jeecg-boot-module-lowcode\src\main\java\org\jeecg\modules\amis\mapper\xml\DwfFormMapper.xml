<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.amis.mapper.DwfFormMapper">
    <!--我的发起-->
    <select id="queryWorkOrder" parameterType="org.jeecg.modules.amis.model.WorkOrder"
            resultType="org.jeecg.modules.amis.model.WorkOrder">
        SELECT
        t.ID_ "id",
        t.BUSINESS_KEY_ "businessKey",
        t.PROC_INST_ID_ "procInstId",
        f.id "formId",
        f.form_name "formName",
        t.PROC_DEF_KEY_ "procDefKey",
        f.model_name "procDefName",
        u.id "startUserId",
        u.realname "startUserName",
        t.START_TIME_ "startTime",
        t.END_TIME_ "endTime",
        t.DURATION_ "duration",
        t.STATE_ "state",
        rt.id_ "taskId",
        CASE
        WHEN rt.TASK_DEF_KEY_ = (
        SELECT
        hai.ACT_ID_
        FROM
        act_hi_actinst hai
        WHERE
        hai.PROC_INST_ID_ = rt.PROC_INST_ID_
        AND hai.ACT_TYPE_ = 'userTask'
        AND hai.ACT_ID_ != (
        SELECT
        start_event.ACT_ID_
        FROM
        act_hi_actinst start_event
        WHERE
        start_event.PROC_INST_ID_ = rt.PROC_INST_ID_
        AND start_event.ACT_TYPE_ = 'startEvent'
        ORDER BY
        start_event.START_TIME_,start_event.SEQUENCE_COUNTER_ ASC
        LIMIT 1
        )
        ORDER BY
        hai.START_TIME_,hai.SEQUENCE_COUNTER_ ASC
        LIMIT 1
        ) THEN
        'Y' ELSE 'N'
        END "firstTask"
        FROM
        ACT_HI_PROCINST t
        JOIN DWF_FORM f ON t.PROC_DEF_KEY_ = f.model_key
        LEFT JOIN ( SELECT * FROM act_ru_task WHERE assignee_ = #{workOrder.startUserId} ) rt ON t.id_ =
        rt.proc_inst_id_
        LEFT JOIN sys_user u ON t.start_user_id_ = u.id
        WHERE
        t.state_ NOT IN ( 'INTERNALLY_TERMINATED' )
        and u.id=#{workOrder.startUserId}
        <if test="workOrder.formName != null and workOrder.formName != ''">
            and f.form_name like CONCAT(CONCAT('%', #{workOrder.formName}), '%')
        </if>
        order by t.start_time_ desc
    </select>
    <!--待办工单-->
    <select id="queryTaskOrder" parameterType="org.jeecg.modules.amis.model.TaskOrder"
            resultType="org.jeecg.modules.amis.model.TaskOrder">
        SELECT
        t.id_ "id",
        f.id "formId",
        f.form_name "formName",
        t.proc_inst_id_ "procInstId",
        p.business_key_ "businessKey",
        p.start_user_id_ "startUserId",
        u.realname "startUserName",
        p.start_time_ "procStartTime",
        f.model_key "procDefKey",
        f.model_name "procDefName",
        t.owner_ "owner",
        t.name_ "taskName",
        t.create_time_ "taskStartTime"
        FROM
        ((
        SELECT
        *,
        CASE

        WHEN rt.TASK_DEF_KEY_ = (
        SELECT
        hai.ACT_ID_
        FROM
        act_hi_actinst hai
        WHERE
        hai.PROC_INST_ID_ = rt.PROC_INST_ID_
        AND hai.ACT_TYPE_ = 'userTask'
        AND hai.ACT_ID_ != (
        SELECT
        start_event.ACT_ID_
        FROM
        act_hi_actinst start_event
        WHERE
        start_event.PROC_INST_ID_ = rt.PROC_INST_ID_
        AND start_event.ACT_TYPE_ = 'startEvent'
        ORDER BY
        start_event.START_TIME_,start_event.SEQUENCE_COUNTER_ ASC
        LIMIT 1
        )
        ORDER BY
        hai.START_TIME_,
        hai.SEQUENCE_COUNTER_ ASC
        LIMIT 1
        ) THEN
        'Y' ELSE 'N'
        END AS FIRST_TASK
        FROM
        act_ru_task rt
        WHERE
        rt.ASSIGNEE_ = #{taskOrder.startUserId}
        )) t,
        act_hi_procinst p,
        dwf_form f,
        sys_user u
        WHERE
        t.proc_inst_id_ = p.id_
        AND p.PROC_DEF_KEY_ = f.model_key
        AND p.start_user_id_ = u.id
        AND T.first_task = 'N'
        <if test="taskOrder.formName != null and taskOrder.formName != ''">
            and f.form_name like CONCAT(CONCAT('%', #{taskOrder.formName}), '%')
        </if>
        <if test="taskOrder.startUserName != null and taskOrder.startUserName != ''">
            and u.realname like CONCAT(CONCAT('%', #{taskOrder.startUserName}), '%')
        </if>
        ORDER BY t.create_time_ DESC
    </select>
    <!--我的经办-->
    <select id="queryHandleOrder" parameterType="org.jeecg.modules.amis.model.WorkOrder"
            resultType="org.jeecg.modules.amis.model.WorkOrder">
        SELECT
        t.ID_ "id",
        t.BUSINESS_KEY_ "businessKey",
        t.PROC_INST_ID_ "procInstId",
        f.id "formId",
        f.form_name "formName",
        t.PROC_DEF_KEY_ "procDefKey",
        f.model_name "procDefName",
        u.id "startUserId",
        u.realname "startUserName",
        t.START_TIME_ "startTime",
        t.END_TIME_ "endTime",
        t.DURATION_ "duration",
        t.STATE_ "state",
        CASE
        WHEN rt.TASK_DEF_KEY_ = (
        SELECT
        hai.ACT_ID_
        FROM
        act_hi_actinst hai
        WHERE
        hai.PROC_INST_ID_ = rt.PROC_INST_ID_
        AND hai.ACT_TYPE_ = 'userTask'
        AND hai.ACT_ID_ != (
        SELECT
        start_event.ACT_ID_
        FROM
        act_hi_actinst start_event
        WHERE
        start_event.PROC_INST_ID_ = rt.PROC_INST_ID_
        AND start_event.ACT_TYPE_ = 'startEvent'
        ORDER BY
        start_event.START_TIME_,start_event.SEQUENCE_COUNTER_ ASC
        LIMIT
        1
        )
        ORDER BY
        hai.START_TIME_,hai.SEQUENCE_COUNTER_ ASC
        LIMIT
        1
        ) THEN 'Y'
        ELSE 'N'
        END "firstTask"
        FROM
        ACT_HI_PROCINST t
        JOIN ACT_RE_PROCDEF proc ON t.proc_def_id_ = proc.id_
        JOIN DWF_FORM f ON t.PROC_DEF_KEY_ = f.model_key
        LEFT JOIN (
        SELECT
        *
        FROM
        act_ru_task
        WHERE
        assignee_ = #{workOrder.startUserId}
        ) rt ON t.id_ = rt.proc_inst_id_
        LEFT JOIN sys_user u ON t.start_user_id_ = u.id
        WHERE
        t.proc_inst_id_ IN (
        SELECT
        O.PROC_INST_ID
        FROM
        ACT_AP_OP_LOG O
        WHERE
        O.OP_USER_ID = #{workOrder.startUserId}
        AND O.OP_TYPE != 'submit'
        )
        AND t.state_ NOT IN ('INTERNALLY_TERMINATED')
        <if test="workOrder.startUserName != null and workOrder.startUserName != ''">
            and u.realname like CONCAT(CONCAT('%', #{workOrder.startUserName}), '%')
        </if>
        <if test="workOrder.formName != null and workOrder.formName != ''">
            and f.form_name like CONCAT(CONCAT('%', #{workOrder.formName}), '%')
        </if>
        order by t.start_time_ desc
    </select>
</mapper>
