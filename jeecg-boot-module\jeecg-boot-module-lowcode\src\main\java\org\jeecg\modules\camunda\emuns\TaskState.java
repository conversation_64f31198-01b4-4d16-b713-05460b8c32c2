package org.jeecg.modules.camunda.emuns;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */

public enum TaskState {
    /**
     * ACTIVE：执行中
     * COMPLETED：完成
     */
    ACTIVE, COMPLETED;
    private static final LinkedHashMap<TaskState, String> NAME_SPACE = new LinkedHashMap<TaskState, String>();

    static {
        NAME_SPACE.put(ACTIVE, "active");
        NAME_SPACE.put(COMPLETED, "completed");
    }

    public String getName() {
        return NAME_SPACE.get(this);
    }
}
