package org.jeecg.modules.camunda.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.camunda.emuns.ActiveType;
import org.jeecg.modules.camunda.emuns.DeployType;
import org.jeecg.modules.camunda.entity.ActApModel;
import org.jeecg.modules.camunda.entity.ActApType;
import org.jeecg.modules.camunda.mapper.ActApModelMapper;
import org.jeecg.modules.camunda.model.UserModel;
import org.jeecg.modules.camunda.service.ActApModelService;
import org.jeecg.modules.camunda.vo.RoleVo;
import org.jeecg.modules.camunda.vo.TaskPositionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/act/app/model")
public class ActApModelController {

    @Autowired
    private ActApModelService actApModelService;
    @Autowired
    private ActApModelMapper actApModelMapper;

    /**
     * 分页列表查询
     *
     * @param actApModel
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "流程应用模板-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> list(ActApModel actApModel,
                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                          HttpServletRequest req) {
        QueryWrapper<ActApModel> queryWrapper = QueryGenerator.initQueryWrapper(actApModel, req.getParameterMap());
        queryWrapper.orderByAsc("cre_date");
        Page<ActApModel> page = new Page<ActApModel>(pageNo, pageSize);
        IPage<ActApModel> pageList = actApModelService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 查询所有
     *
     * @param actApModel
     * @return
     */
    @AutoLog(value = "流程应用模板-查询所有流程模型")
    @GetMapping(value = "/listAll")
    public Result<?> list(ActApModel actApModel) {
        QueryWrapper<ActApModel> queryWrapper = new QueryWrapper<ActApModel>();
        queryWrapper.orderByAsc("cre_date");
        List<ActApModel> list = actApModelMapper.selectList(queryWrapper);
        return Result.OK(list);
    }

    /**
     * 分页列表查询
     *
     * @param actApModel
     * @param req
     * @return
     */
    @AutoLog(value = "流程应用模板-根据权限查询所有模板")
    @GetMapping(value = "/authList")
    public Result<?> authList(ActApModel actApModel,
                              HttpServletRequest req) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<ActApModel> modelList = actApModelMapper.queryModelByAuth(sysUser.getId(), actApModel.getTypeId());
        return Result.OK(modelList);
    }

    /**
     * 保存
     *
     * @param actApModel
     * @return
     */
    @AutoLog(value = "流程应用模板-保存")
    @PostMapping(value = "/saveOrUpdate")
    public Result<?> saveOrUpdate(@RequestBody ActApModel actApModel) {
        if (StringUtils.isEmpty(actApModel.getId())) {
            actApModel.setModelContent(actApModelService.initXml(actApModel));
        }
        actApModelService.saveOrUpdate(actApModel);
        return Result.OK(actApModel);
    }

    /**
     * 流程变更
     *
     * @param actApModel
     * @return
     */
    @AutoLog(value = "流程应用模板-流程变更")
    @PostMapping(value = "/upProcess")
    public Result<?> upProcess(@RequestBody ActApModel actApModel) {
        ActApModel model = actApModelService.getById(actApModel.getId());
        model.setModelContent(actApModel.getModelContent());
        model.setSeq(actApModel.getSeq());
        actApModelService.saveOrUpdate(model);
        return Result.OK(model);
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ActApModel model = actApModelService.getById(id);
        return Result.OK(model);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "流程应用模板-通过id删除")
    @DeleteMapping(value = "/delete")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        ActApModel model = actApModelService.getById(id);
        if (model.getDeployState().equals(DeployType.INIT.getName())) {
            actApModelService.removeById(id);
        } else {
            return Result.error("流程发布后禁止删除！");
        }
        return Result.OK("删除成功!");
    }

    /**
     * 改变模型使用状态
     *
     * @param actApModel
     * @return
     */
    @AutoLog(value = "流程应用模板-改变模型使用状态")
    @PostMapping(value = "/changeState")
    public Result<?> changeState(@RequestBody ActApModel actApModel) {
        ActApModel model = actApModelService.getById(actApModel.getId());
        model.setUseState(actApModel.getUseState());
        actApModelService.saveOrUpdate(model);
        if (model.getUseState().equals(ActiveType.SUSPEND.getName())) {
            return Result.OK("流程已停用!");
        } else {
            return Result.OK("流程已激活!");
        }
    }

    /**
     * 分页列表查询
     *
     * @param userModel
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "流程应用模板-待选人员")
    @GetMapping(value = "/userList")
    public Result<?> userList(UserModel userModel,
                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                              HttpServletRequest req) {
        Page<UserModel> page = new Page<UserModel>(pageNo, pageSize);
        IPage<UserModel> pageList = actApModelMapper.queryUserPage(page, userModel.getRealname());
        return Result.OK(pageList);
    }

    /**
     * 不页列表查询已选人员
     *
     * @param userIds
     * @return
     */
    @AutoLog(value = "流程应用模板-已选人员")
    @GetMapping(value = "/userSelect")
    public Result<?> userSelect(String userIds) {
        List<UserModel> userList = new ArrayList<UserModel>();
        if (StringUtils.isNotEmpty(userIds)) {
            userList = actApModelMapper.queryUserSelect(Arrays.asList(userIds.split(",")));
        }
        return Result.OK(userList);
    }

    /**
     * 分页列表查询
     *
     * @param roleVo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "流程应用模板-待选角色")
    @GetMapping(value = "/roleList")
    public Result<?> roleList(RoleVo roleVo,
                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                              HttpServletRequest req) {
        Page<RoleVo> page = new Page<RoleVo>(pageNo, pageSize);
        IPage<RoleVo> pageList = actApModelMapper.queryRolePage(page, roleVo.getRoleName());
        return Result.OK(pageList);
    }

    /**
     * 不页列表查询已选角色
     *
     * @param roleIds
     * @return
     */
    @AutoLog(value = "流程应用模板-已选角色")
    @GetMapping(value = "/roleSelect")
    public Result<?> roleSelect(String roleIds) {
        List<RoleVo> roleList = new ArrayList<RoleVo>();
        if (StringUtils.isNotEmpty(roleIds)) {
            roleList = actApModelMapper.queryRoleSelect(Arrays.asList(roleIds.split(",")));
        }
        return Result.OK(roleList);
    }

    /**
     * 分页列表查询
     *
     * @param positionVo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "流程应用模板-待选职位")
    @GetMapping(value = "/positionList")
    public Result<?> positionList(TaskPositionVo positionVo,
                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                  HttpServletRequest req) {
        Page<TaskPositionVo> page = new Page<TaskPositionVo>(pageNo, pageSize);
        IPage<TaskPositionVo> pageList = actApModelMapper.queryPositionListPage(page, positionVo.getName());
        return Result.OK(pageList);
    }

    /**
     * 不页列表查询已选职位
     *
     * @param positionIds
     * @return
     */
    @AutoLog(value = "流程应用模板-已选职位")
    @GetMapping(value = "/positionSelect")
    public Result<?> positionSelect(String positionIds) {
        List<TaskPositionVo> positionList = new ArrayList<TaskPositionVo>();
        if (StringUtils.isNotEmpty(positionIds)) {
            positionList = actApModelMapper.queryPositionSelect(Arrays.asList(positionIds.split(",")));
        }
        return Result.OK(positionList);
    }

    /**
     * 权限内所有流程业务
     *
     * @return
     */
    @AutoLog(value = "流程应用模板-权限内所有流程业务")
    @GetMapping(value = "/selfTypeList")
    public Result<?> selfTypeList() {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<ActApType> typeList = actApModelMapper.queryApType(sysUser.getId());
        return Result.OK(typeList);
    }

}
