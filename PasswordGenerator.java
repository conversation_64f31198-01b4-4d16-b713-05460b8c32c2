import java.security.Key;
import java.security.SecureRandom;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.PBEParameterSpec;

/**
 * JeecgBoot Password Generator
 */
public class PasswordGenerator {
    
    public static final String ALGORITHM = "PBEWithMD5AndDES";
    private static final int ITERATIONCOUNT = 1000;
    
    /**
     * Generate random salt (8 characters)
     */
    public static String generateSalt() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder salt = new StringBuilder();
        for (int i = 0; i < 8; i++) {
            salt.append(chars.charAt(random.nextInt(chars.length())));
        }
        return salt.toString();
    }
    
    /**
     * Generate PBE key
     */
    private static Key getPbeKey(String password) {
        SecretKeyFactory keyFactory;
        SecretKey secretKey = null;
        try {
            keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
            PBEKeySpec keySpec = new PBEKeySpec(password.toCharArray());
            secretKey = keyFactory.generateSecret(keySpec);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return secretKey;
    }
    
    /**
     * Encrypt plaintext
     */
    public static String encrypt(String plaintext, String password, String salt) {
        Key key = getPbeKey(password);
        byte[] encipheredData = null;
        PBEParameterSpec parameterSpec = new PBEParameterSpec(salt.getBytes(), ITERATIONCOUNT);
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, key, parameterSpec);
            encipheredData = cipher.doFinal(plaintext.getBytes("utf-8"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bytesToHexString(encipheredData);
    }
    
    /**
     * Convert byte array to hex string
     */
    public static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }
    
    public static void main(String[] args) {
        String username = "admin";
        String plainPassword = "123456";
        
        // Generate new salt
        String newSalt = generateSalt();
        
        // Encrypt password with new salt
        String encryptedPassword = encrypt(plainPassword, username, newSalt);
        
        System.out.println("=== JeecgBoot Password Generator ===");
        System.out.println("Username: " + username);
        System.out.println("Plain Password: " + plainPassword);
        System.out.println("New Salt: " + newSalt);
        System.out.println("Encrypted Password: " + encryptedPassword);
        System.out.println();
        System.out.println("=== SQL Update Statement ===");
        System.out.println("UPDATE sys_user SET ");
        System.out.println("  password = '" + encryptedPassword + "',");
        System.out.println("  salt = '" + newSalt + "'");
        System.out.println("WHERE username = 'admin';");
        System.out.println();
        System.out.println("Execute this SQL in your database to set admin password to '123456'");
    }
}
