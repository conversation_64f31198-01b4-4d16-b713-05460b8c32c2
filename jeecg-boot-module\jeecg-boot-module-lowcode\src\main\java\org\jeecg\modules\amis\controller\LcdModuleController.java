package org.jeecg.modules.amis.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.amis.entity.LcdModule;
import org.jeecg.modules.amis.service.LcdModuleService;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 低代码模块表管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/lcd/module")
@Tag(name = "低代码模块表管理")
public class LcdModuleController {

    @Resource
    private LcdModuleService lcdModuleService;

    /**
     * 分页列表查询
     *
     * @param lcdModule
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */

    @ResponseBody
    @GetMapping("/list")
    @Operation(summary = "分页查询低代码模块")
    public Result<IPage<LcdModule>> queryPageList(LcdModule lcdModule,
                                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        Result<IPage<LcdModule>> result = new Result<IPage<LcdModule>>();
        //默认查询未删除的数据
        lcdModule.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
        QueryWrapper<LcdModule> queryWrapper = QueryGenerator.initQueryWrapper(lcdModule, req.getParameterMap());
        Page<LcdModule> page = new Page<>(pageNo, pageSize);
        IPage<LcdModule> pageList = lcdModuleService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
     * 查询低代码模块列表
     * 该接口用于查询所有未删除的低代码模块，并按创建时间降序排列返回。
     *
     * @param lcdModule 查询条件，目前仅使用其中的delFlag属性，设置为未删除状态
     * @return Result<List < LcdModule>> 包含查询结果的Response对象，成功时包含低代码模块列表
     */
    @ResponseBody
    @GetMapping("/listAll")
    @Operation(summary = "查询低代码模块")
    public Result<List<LcdModule>> queryAllList(LcdModule lcdModule) {
        Result<List<LcdModule>> result = new Result<List<LcdModule>>();
        //默认查询未删除的数据
        lcdModule.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
        LambdaQueryWrapper<LcdModule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LcdModule::getDelFlag, CommonConstant.DEL_FLAG_0);
        queryWrapper.orderByDesc(LcdModule::getCreateTime);
        List<LcdModule> dataList = lcdModuleService.list(queryWrapper);
        result.setSuccess(true);
        result.setResult(dataList);
        return result;
    }

    /**
     * 添加新数据并保存到数据库
     *
     * @param lcdModule
     * @return
     */
    @PostMapping(value = "/add")
    @Operation(summary = "添加模低代码模块")
    public Result<LcdModule> add(@RequestBody LcdModule lcdModule, HttpServletRequest request) {
        Result<LcdModule> result = new Result<LcdModule>();
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            lcdModule.setCreateBy(sysUser.getRealname());
            lcdModule.setCreateTime(new Date());
            lcdModuleService.save(lcdModule);
            result.success("添加成功！");
        } catch (Exception e) {
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 编辑数据
     *
     * @param lcdModule
     * @return
     */
    @PostMapping(value = "/edit")
    @Operation(summary = "编辑低代码模块")
    public Result<LcdModule> edit(@RequestBody LcdModule lcdModule, HttpServletRequest request) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Result<LcdModule> result = new Result<LcdModule>();
        LcdModule lcdModuleEntity = lcdModuleService.getById(lcdModule.getId());
        if (lcdModuleEntity == null) {
            result.error500("未找到对应实体");
        } else {
            lcdModule.setUpdateBy(sysUser.getRealname());
            lcdModule.setUpdateTime(new Date());
            lcdModuleService.updateById(lcdModule);
            result.success("修改成功!");
        }
        return result;
    }

    /**
     * 根据id获取对象
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/queryById")
    @Operation(summary = "根据id获取低代码模块")
    public Result<LcdModule> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<LcdModule> result = new Result<LcdModule>();
        LcdModule lcdModule = lcdModuleService.getById(id);
        if (lcdModule == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(lcdModule);
            result.setSuccess(true);
        }
        return result;
    }


    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @DeleteMapping(value = "/delete")
    @Operation(summary = "根据id删除模网格人员")
    public Result<LcdModule> delete(@RequestParam(name = "id", required = true) String id) {
        Result<LcdModule> result = new Result<LcdModule>();
        LcdModule lcdModule = lcdModuleService.getById(id);
        if (lcdModule == null) {
            return Result.error("未找到对应实体！");
        } else {
            //删除实体对象
            lcdModuleService.removeById(id);
            return Result.ok("删除成功！");
        }
    }
}
