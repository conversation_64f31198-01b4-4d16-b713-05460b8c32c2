package org.jeecg.modules.camunda.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 流程应用类型表
 *
 * <AUTHOR>
 * @TableName ACT_AP_TYPE
 */
@TableName(value = "ACT_AP_TYPE")
@Data
public class ActApType implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 应用id;主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 应用编码
     */
    private String typeCode;

    /**
     * 应用名称
     */
    private String typeName;

    /**
     * 状态
     */
    private String state;

    /**
     * 序号
     */
    private Double seq;

}