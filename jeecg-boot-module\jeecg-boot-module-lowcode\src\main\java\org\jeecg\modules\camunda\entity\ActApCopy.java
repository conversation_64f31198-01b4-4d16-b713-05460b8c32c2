package org.jeecg.modules.camunda.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 抄送表
 *
 * <AUTHOR>
 * @TableName ACT_AP_COPY
 */
@TableName(value = "ACT_AP_COPY")
@Data
public class ActApCopy implements Serializable {
    /**
     * 抄送id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 接收人id
     */
    private String recvUserId;

    /**
     * 接收人
     */
    private String recvUser;

    /**
     * 抄送时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 阅读时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date readTime;

    /**
     * 业务编码
     */
    private String businessKey;

    /**
     * 流程标识
     */
    private String procKey;

    /**
     * 流程名称
     */
    private String procName;

    /**
     * 流程实例id
     */
    private String procInstId;

    /**
     * 发起人id
     */
    private String startUserId;

    /**
     * 发起人
     */
    private String startUser;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}