<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.camunda.mapper.ActApOpLogMapper">
    <!--我的发起-->
    <select id="queryHisProcinst" parameterType="org.jeecg.modules.camunda.model.ActHiProcinst"
            resultType="org.jeecg.modules.camunda.model.ActHiProcinst">
        select t.id_ "id",
        t.proc_inst_id_ "procInstId",
        t.business_key_ "businessKey",
        t.proc_def_key_ "procDefKey",
        proc.name_ "procDefName",
        t.proc_def_id_ "procDefId",
        t.start_time_ "startTime",
        t.end_time_ "endTime",
        t.removal_time_ "removalTime",
        t.duration_ "duration",
        t.start_user_id_ "startUserId",
        u.realname "startUserName",
        t.start_act_id_ "startActId",
        t.end_act_id_ "endActId",
        t.super_process_instance_id_ "superProcessInstanceId",
        t.root_proc_inst_id_ "rootProcInstId",
        t.super_case_instance_id_ "superCaseInstanceId",
        t.case_inst_id_ "caseInstId",
        t.delete_reason_ "deleteReason",
        t.tenant_id_ "tenantId",
        t.state_ "state"
        FROM
        ACT_HI_PROCINST t
        JOIN
        ACT_RE_PROCDEF proc ON t.proc_def_id_ = proc.id_
        LEFT JOIN
        sys_user u ON t.start_user_id_ = u.id
        where
        t.state_ not in('INTERNALLY_TERMINATED')
        <if test="procinst.procDefName != null and procinst.procDefName != ''">
            and proc.name_ like CONCAT(CONCAT('%', #{procinst.procDefName}), '%')
        </if>
        order by t.start_time_ desc
    </select>
    <!--我的待办任务-->
    <select id="queryRunTask" parameterType="org.jeecg.modules.camunda.model.ActRuTask"
            resultType="org.jeecg.modules.camunda.model.ActRuTask">
        select t.id_ "id",
        t.rev_ "rev",
        t.execution_id_ "executionId",
        t.proc_inst_id_ "procInstId",
        p.business_key_ "businessKey",
        p.start_user_id_ "startUserId",
        u.realname "startUserName",
        p.start_time_ "procStartTime",
        t.proc_def_id_ "procDefId",
        d.key_ "procDefKey",
        d.name_ "procDefName",
        t.case_execution_id_ "caseExecutionId",
        t.case_inst_id_ "caseInstId",
        t.case_def_id_ "caseDefId",
        t.name_ "name",
        t.parent_task_id_ "parentTaskId",
        t.description_ "description",
        t.task_def_key_ "taskDefKey",
        t.owner_ "owner",
        t.assignee_ "assignee",
        t.delegation_ "delegation",
        t.priority_ "priority",
        t.create_time_ "createTime",
        t.last_updated_ "lastUpdated",
        t.due_date_ "dueDate",
        t.follow_up_date_ "followUpDate",
        t.suspension_state_ "suspensionState",
        t.tenant_id_ "tenantId"
        from act_ru_task t,
        act_hi_procinst p,
        act_re_procdef d,
        sys_user u
        where t.proc_inst_id_ = p.id_
        and p.proc_def_id_ = d.id_
        and p.start_user_id_ = u.id
        and t.assignee_ = #{runTask.assignee}
        <if test="runTask.procDefName != null and runTask.procDefName != ''">
            and d.name_ like CONCAT(CONCAT('%', #{runTask.procDefName}), '%')
        </if>
        order by t.create_time_ desc
    </select>
    <!--我的经办-->
    <select id="queryHandleProcinst" parameterType="org.jeecg.modules.camunda.model.ActHiProcinst"
            resultType="org.jeecg.modules.camunda.model.ActHiProcinst">
        select t.id_ "id",
        t.proc_inst_id_ "procInstId",
        t.business_key_ "businessKey",
        t.proc_def_key_ "procDefKey",
        proc.name_ "procDefName",
        t.proc_def_id_ "procDefId",
        t.start_time_ "startTime",
        t.end_time_ "endTime",
        t.removal_time_ "removalTime",
        t.duration_ "duration",
        t.start_user_id_ "startUserId",
        u.realname "startUserName",
        t.start_act_id_ "startActId",
        t.end_act_id_ "endActId",
        t.super_process_instance_id_ "superProcessInstanceId",
        t.root_proc_inst_id_ "rootProcInstId",
        t.super_case_instance_id_ "superCaseInstanceId",
        t.case_inst_id_ "caseInstId",
        t.delete_reason_ "deleteReason",
        t.tenant_id_ "tenantId",
        t.state_ "state"
        FROM
        ACT_HI_PROCINST t
        JOIN
        ACT_RE_PROCDEF proc ON t.proc_def_id_ = proc.id_
        LEFT JOIN
        sys_user u ON t.start_user_id_ = u.id
        where
         t.proc_inst_id_ IN
         (SELECT O.PROC_INST_ID
            FROM ACT_AP_OP_LOG O
            WHERE O.OP_USER_ID = #{procinst.startUserId}
            AND O.OP_TYPE
        != 'submit')
        <if test="procinst.procDefName != null and procinst.procDefName != ''">
            and proc.name_ like CONCAT(CONCAT('%', #{procinst.procDefName}), '%')
        </if>
        and t.state_ not in ('INTERNALLY_TERMINATED')
        order by t.start_time_ desc
    </select>
    <!--抄送我的-->
    <select id="querySendProcinst" parameterType="org.jeecg.modules.camunda.model.ActHiProcinst"
            resultType="org.jeecg.modules.camunda.model.ActHiProcinst">
        select t.id_ "id",
        t.proc_inst_id_ "procInstId",
        t.business_key_ "businessKey",
        t.proc_def_key_ "procDefKey",
        proc.name_ "procDefName",
        t.proc_def_id_ "procDefId",
        t.start_time_ "startTime",
        t.end_time_ "endTime",
        t.removal_time_ "removalTime",
        t.duration_ "duration",
        t.start_user_id_ "startUserId",
        u.realname "startUserName",
        t.start_act_id_ "startActId",
        t.end_act_id_ "endActId",
        t.super_process_instance_id_ "superProcessInstanceId",
        t.root_proc_inst_id_ "rootProcInstId",
        t.super_case_instance_id_ "superCaseInstanceId",
        t.case_inst_id_ "caseInstId",
        t.delete_reason_ "deleteReason",
        t.tenant_id_ "tenantId",
        t.state_ "state"
        FROM
        ACT_HI_PROCINST t
        INNER JOIN ACT_RE_PROCDEF proc ON t.proc_def_id_ = proc.id_
        LEFT JOIN sys_user u ON t.start_user_id_ = u.id
        WHERE
         t.proc_inst_id_ IN
        (select c.proc_inst_id
        from ACT_AP_COPY c
        where c.recv_user_id = #{procinst.startUserId})
        <if test="procinst.procDefName != null and procinst.procDefName != ''">
            and proc.name_ like CONCAT(CONCAT('%', #{procinst.procDefName}), '%')
        </if>
        and t.state_ not in ('INTERNALLY_TERMINATED')
        order by t.start_time_ desc
    </select>
</mapper>
