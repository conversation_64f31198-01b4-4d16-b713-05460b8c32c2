package org.jeecg.modules.amis.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 待办工单
 *
 * <AUTHOR>
 */
@Data
public class TaskOrder implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String id;
    private String formId;
    private String formName;
    private String procInstId;
    private String businessKey;
    private String startUserId;
    private String startUserName;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date procStartTime;
    private String procDefKey;
    private String procDefName;
    private String owner;
    private String taskName;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskStartTime;
}
