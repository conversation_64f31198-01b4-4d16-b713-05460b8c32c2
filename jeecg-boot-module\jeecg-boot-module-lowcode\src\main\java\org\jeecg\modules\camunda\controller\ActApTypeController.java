package org.jeecg.modules.camunda.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.camunda.entity.ActApType;
import org.jeecg.modules.camunda.mapper.ActApTypeMapper;
import org.jeecg.modules.camunda.service.ActApTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/act/app/type")
public class ActApTypeController {
    @Autowired
    private ActApTypeService actApTypeService;
    @Autowired
    private ActApTypeMapper actApTypeMapper;

    /**
     * 分页列表查询
     *
     * @param actApType
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "流程应用类型管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> list(ActApType actApType, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                          HttpServletRequest req) {
        QueryWrapper<ActApType> queryWrapper = QueryGenerator.initQueryWrapper(actApType, req.getParameterMap());
        queryWrapper.orderByAsc("seq");
        Page<ActApType> page = new Page<ActApType>(pageNo, pageSize);
        IPage<ActApType> pageList = actApTypeService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 查询所有
     *
     * @param actApType
     * @return
     */
    @AutoLog(value = "流程应用类型管理-查询所有")
    @GetMapping(value = "/listAll")
    public Result<?> list(ActApType actApType) {
        QueryWrapper<ActApType> queryWrapper = new QueryWrapper<ActApType>();
        queryWrapper.orderByAsc("seq");
        List<ActApType> list = actApTypeMapper.selectList(queryWrapper);
        return Result.OK(list);
    }

    /**
     * 添加
     *
     * @param actApType
     * @return
     */
    @AutoLog(value = "流程应用类型管理-添加")
    @PostMapping(value = "/saveOrUpdate")
    public Result<?> saveOrUpdate(@RequestBody ActApType actApType) {
        actApTypeService.saveOrUpdate(actApType);
        return Result.OK(actApType);
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        ActApType actApType = actApTypeService.getById(id);
        return Result.OK(actApType);
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "流程应用类型管理-通过id删除")
    @DeleteMapping(value = "/delete")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        actApTypeService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 根据名称查询分页
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @GetMapping(value = "/listByName")
    public Result<?> listByName(@RequestParam(name = "typeName", required = false) String typeName,
                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                HttpServletRequest req) {
        QueryWrapper<ActApType> queryWrapper = new QueryWrapper<ActApType>();
        if (StringUtils.isNotEmpty(typeName)) {
            queryWrapper.like("type_name", typeName);
        }
        queryWrapper.orderByAsc("seq");
        Page<ActApType> page = new Page<ActApType>(pageNo, pageSize);
        IPage<ActApType> pageList = actApTypeService.page(page, queryWrapper);
        return Result.OK(pageList);
    }
}
