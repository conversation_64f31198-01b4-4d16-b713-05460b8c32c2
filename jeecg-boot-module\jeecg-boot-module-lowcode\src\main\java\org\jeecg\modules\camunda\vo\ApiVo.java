package org.jeecg.modules.camunda.vo;

import lombok.Data;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ApiVo {
    private static final long serialVersionUID = 1L;
    /**
     * 模型id
     */
    private String modelId;
    /**
     * 模型内容
     */
    private String modelContent;
    /**
     * 流程key
     */
    private String processKey;
    /**
     * 业务key
     */
    private String businessKey;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 任务标识
     */
    private String taskKey;
    /**
     * 办理意见
     */
    private String comment;
    private TaskUserVo user;
    /**
     * 实例id
     */
    private String procInstId;
    private List<TaskUserVo> userList;
    private List<VariablesVo> variables;
    /**
     * 表单编码
     */
    private String formCode;
    /**
     * 操作类型
     */
    private String opType;
    /**
     * 操作
     */
    private String opDesc;
    /**
     * 是否允许编辑表单
     */
    private String allowEditForm;
    private HashMap<String, Object> formData;
    private String nextTaskKey;
    private String nextUserIds;
    /**
     * 退回指定节点
     */
    private String backTaskKey;
    /**
     * 目标节点key
     */
    private String targetKey;

    /**
     * 动态表单id
     */
    private String formId;
}
