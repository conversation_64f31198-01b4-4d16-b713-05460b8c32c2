package org.jeecg.modules.camunda.util;

import com.xkcoding.http.util.StringUtil;
import org.dom4j.*;
import org.jeecg.modules.camunda.util.pojo.XmlElement;
import org.jeecg.modules.camunda.vo.ColVo;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ParseXmlUtil {
    private static final Pattern PATTERN = Pattern.compile("\\$(\\w+)\\$");
    private static final String IS_NOT_EMPTY = "isNotEmpty";
    private static final String IS_EMPTY = "isEmpty";
    private static final String IS_NOT_EQUAL = "isNotEqual";
    private static final String IS_EQUAL = "isEqual";
    private static final String DYNAMIC = "dynamic";
    private static final String CHAR_38 = "&";

    /**
     * 转换SQL
     *
     * @param model
     * @param sql
     * @return
     */
    public static String transformSql(String sql, Map<String, Object> model) {
        Matcher matcher = PATTERN.matcher(sql);
        while (matcher.find()) {
            String key = matcher.group(1);
            if (model.containsKey(key)) {
                sql = sql.replace("$" + key + "$", model.get(key).toString());
            }
        }
        return sql;
    }

    /**
     * 把传入的sql字符串转化为list<XmlElement>的格式
     *
     * @param sql 要解析的sql语句
     * <AUTHOR>
     */
    public List<XmlElement> generateXmlElementList(String sql) {
        sql = ("<sqlParser>" + sql + "</sqlParser>").replace("\n", " ").replace("\t", " ");
        List<XmlElement> elementList = new ArrayList<XmlElement>();
        Document document;
        try {
            document = DocumentHelper.parseText(sql);
            Element root = document.getRootElement();
            generateXmlElement(root, elementList);
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        return elementList;
    }

    /**
     * 根据xml的节点，把节点信息保存到list中
     *
     * @param el   xml的节点
     * @param list 组装好的数据集
     * <AUTHOR>
     */
    private void generateXmlElement(Element el, List<XmlElement> list) {
        List content = el.content();
        if (content != null) {
            for (int i = 0; i < content.size(); i++) {
                Node node = (Node) content.get(i);
                XmlElement entity = new XmlElement();
                entity.setNode(node);
                List<XmlElement> childList = new ArrayList<XmlElement>();
                entity.setChildren(childList);
                switch (node.getNodeType()) {
                    case Node.TEXT_NODE:
                        list.add(entity);
                        break;
                    case Node.ELEMENT_NODE:
                        generateXmlElement((Element) node, childList);
                        list.add(entity);
                    default:
                        break;
                }
            }
        }
    }

    /**
     * 根据参数是否有当前结点的属性设置节点是否有效
     *
     * @param xle   节点
     * @param model 参数
     * <AUTHOR>
     */
    public void setNodeValid(XmlElement xle, Map<String, Object> model) {
        Node node = xle.getNode();
        if (node.getNodeType() == Node.TEXT_NODE) {
            xle.setIsValid(true);
        } else {
            if (node.getName().equals(IS_NOT_EMPTY)) {
                Attribute pro = ((Element) node).attribute("property");
                if (pro != null) {
                    String key = pro.getValue();
                    if (model.containsKey(key) && model.get(key) != null) {
                        if (!StringUtil.isEmpty(model.get(key).toString())) {
                            xle.setIsValid(true);
                        } else {
                            xle.setIsValid(false);
                        }
                    } else {
                        xle.setIsValid(false);
                    }
                } else {
                    xle.setIsValid(true);
                }
            } else if (node.getName().equals(IS_EMPTY)) {
                Attribute pro = ((Element) node).attribute("property");
                if (pro != null) {
                    String key = pro.getValue();
                    if (model.containsKey(key) && !StringUtil.isEmpty(model.get(key).toString())) {
                        xle.setIsValid(false);
                    } else {
                        xle.setIsValid(true);
                    }
                } else {
                    xle.setIsValid(true);
                }
            } else if (node.getName().equals(IS_NOT_EQUAL)) {
                Attribute pro = ((Element) node).attribute("property");
                Attribute comparepro = ((Element) node).attribute("compareValue");
                if (pro != null && comparepro != null) {
                    String key = pro.getValue();
                    String comvalue = comparepro.getValue();

                    if (model.containsKey(key) && !StringUtil.isEmpty(model.get(key).toString())) {
                        String value = model.get(key).toString();
                        if (!value.equals(comvalue)) {
                            xle.setIsValid(true);
                        } else {
                            xle.setIsValid(false);
                        }
                    } else {
                        xle.setIsValid(false);
                    }
                } else {
                    xle.setIsValid(false);
                }
            } else if (node.getName().equals(IS_EQUAL)) {
                Attribute pro = ((Element) node).attribute("property");
                Attribute comparepro = ((Element) node).attribute("compareValue");
                if (pro != null && comparepro != null) {
                    if (model.containsKey(pro.getValue()) && !StringUtil.isEmpty(model.get(pro.getValue()).toString())) {
                        if (model.get(pro.getValue()).toString().equals(comparepro.getValue())) {
                            xle.setIsValid(true);
                        } else {
                            xle.setIsValid(false);
                        }
                    } else {
                        xle.setIsValid(false);
                    }
                } else {
                    xle.setIsValid(false);
                }
            } else {
                xle.setIsValid(true);
            }
        }
        if (xle.getIsValid()) {
            List<XmlElement> list = xle.getChildren();
            for (XmlElement el : list) {
                setNodeValid(el, model);
            }
        }
    }

    /**
     * 根据参数是否有当前结点的属性设置节点是否有效
     *
     * @param xle   节点
     * @param model 参数
     * @attentionEmptyStr model中的包含属性，但没有值， 这个属性当空传处理
     * <AUTHOR>
     */
    public void setNodeValid(XmlElement xle, HashMap<String, Object> model, Boolean attentionEmptyStr) {
        Node node = xle.getNode();
        if (node.getNodeType() == Node.TEXT_NODE) {
            xle.setIsValid(true);
        } else {
            //如果是属性节点
            //isNotEmpty 标签
            if (node.getName().equals(IS_NOT_EMPTY)) {
                Attribute pro = ((Element) node).attribute("property");
                if (pro != null) {
                    String key = pro.getValue();
                    if (model.containsKey(key)) {
                        xle.setIsValid(true);
                    } else {
                        xle.setIsValid(false);
                    }
                } else {
                    xle.setIsValid(true);
                }
            } else if (node.getName().equals(IS_EMPTY)) {
                Attribute pro = ((Element) node).attribute("property");
                if (pro != null) {
                    String key = pro.getValue();
                    if (model.containsKey(key)) {
                        xle.setIsValid(false);
                    } else {
                        xle.setIsValid(true);
                    }
                } else {
                    xle.setIsValid(true);
                }
            } else if (node.getName().equals(IS_NOT_EQUAL)) {
                Attribute pro = ((Element) node).attribute("property");
                Attribute comparepro = ((Element) node).attribute("compareValue");
                if (pro != null && comparepro != null) {
                    String key = pro.getValue();
                    String comvalue = comparepro.getValue();

                    if (model.containsKey(key)) {
                        String value = model.get(key).toString();
                        if (!value.equals(comvalue)) {
                            xle.setIsValid(true);
                        } else {
                            xle.setIsValid(false);
                        }
                    } else {
                        xle.setIsValid(false);
                    }
                } else {
                    xle.setIsValid(false);
                }
            } else if (node.getName().equals(IS_EQUAL)) {
                Attribute pro = ((Element) node).attribute("property");
                Attribute comparepro = ((Element) node).attribute("compareValue");
                if (pro != null && comparepro != null) {
                    String key = pro.getValue();
                    String comvalue = comparepro.getValue();
                    if (model.containsKey(key)) {
                        String value = model.get(key).toString();
                        if (value.equals(comvalue)) {
                            xle.setIsValid(true);
                        } else {
                            xle.setIsValid(false);
                        }
                    } else {
                        xle.setIsValid(false);
                    }
                } else {
                    xle.setIsValid(false);
                }
            } else {
                xle.setIsValid(true);
            }
        }
        if (xle.getIsValid()) {
            List<XmlElement> list = xle.getChildren();
            for (XmlElement el : list) {
                setNodeValid(el, model, attentionEmptyStr);
            }
        }
    }

    /**
     * 设置XmlElement中的节点的连接字符是否可用，需要在XmlElement设置完isValid之后用
     *
     * @param xle
     * <AUTHOR>
     */
    public void setNodePrepend(XmlElement xle) {
        xle.setIsPrepend(getNodePrepend(xle));
        Boolean isFirst = true;
        List<XmlElement> list = xle.getChildren();
        for (int i = 0; i < list.size(); i++) {
            XmlElement el = list.get(i);
            if (el.getIsValid()) {
                el.setIsPrepend(getNodePrepend(el));
                //如果父层是dynamic节点，第一个节点的连接符需要去掉
                if (!isFirst || !el.getIsPrepend() || el.getNode().getNodeType() == Node.TEXT_NODE
                        || !xle.getNode().getName().equals(DYNAMIC)) {
                    continue;
                }
                int j = 0;
                for (; j < i; j++) {
                    XmlElement cel = list.get(j);
                    if (cel.getIsValid()) {
                        if (cel.getNode().getNodeType() == Node.TEXT_NODE && !StringUtil.isEmpty(cel.getNode().getText().trim())) {
                            isFirst = false;
                            break;
                        }
                    }
                }

                if (i == j) {
                    el.setIsPrepend(false);
                    isFirst = false;
                }

            }
        }
    }

    /**
     * 获取当前结点的连接符是否可用
     *
     * @param xle
     * @return
     */
    private Boolean getNodePrepend(XmlElement xle) {
        List<XmlElement> list = xle.getChildren();
        Boolean havePrepend = false;
        for (XmlElement el : list) {
            //如果当前子节点无效 自动跳转到下个子节点
            if (!el.getIsValid()) {
                continue;
            }
            if (el.getNode().getNodeType() == Node.TEXT_NODE) {
                if (!StringUtil.isEmpty(el.getNode().getText().trim())) {
                    havePrepend = true;
                    break;
                }
            } else {
                havePrepend = getNodePrepend(el);
                if (havePrepend) {
                    break;
                }
            }
        }
        return havePrepend;
    }

    /**
     * 解析xml的语句
     *
     * @param list
     * @param sqlBuffer
     * @return
     */
    public StringBuffer parseSql(List<XmlElement> list, StringBuffer sqlBuffer) {
        for (int i = 0; i < list.size(); i++) {
            XmlElement xle = list.get(i);
            Node node = xle.getNode();
            if (xle.getIsValid()) {
                if (node.getNodeType() == Node.TEXT_NODE) {
                    //文本节点直接拼装
                    sqlBuffer.append(xle.getNode().getText());
                } else {
                    if (xle.getIsPrepend() != null && xle.getIsPrepend()) {
                        Attribute pro = ((Element) node).attribute("prepend");
                        if (pro != null) {
                            sqlBuffer.append(" " + pro.getValue() + " ");
                        }

                    }
                    parseSql(xle.getChildren(), sqlBuffer);

                }
            }
        }
        return sqlBuffer;
    }

    /**
     * 把sql中的# #变量用model中的变量进行替换
     *
     * @param sql
     * @param model
     * @return
     */
    public static String replaceSqlValue(String sql, Map<String, Object> model) {
        Set keySet = model.keySet();
        Iterator it = keySet.iterator();
        while (it.hasNext()) {
            String key = (String) it.next();
            Object value = model.get(key);
            if (value == null) {
                continue;
            }
            if (value instanceof String) {
                sql = sql.replace("#" + key + "#", "'" + value + "'");
            } else {
                sql = sql.replace("#" + key + "#", value.toString());
            }
        }
        return sql;
    }

    /**
     * 把sql中的# #变量用model中的变量进行替换
     *
     * @param sql
     * @param model
     * @param clobColList
     * @return
     */
    public static String replaceSaveSqlValue(String sql, Map<String, Object> model, List<ColVo> clobColList) {
        Set keySet = model.keySet();
        Iterator it = keySet.iterator();
        while (it.hasNext()) {
            String key = (String) it.next();
            Object value = model.get(key);
            if (value == null) {
                continue;
            }
            ColVo clobCol = new ColVo();
            List<ColVo> filterCols = clobColList.stream().filter(x -> x.getCamelName().equals(key)).collect(Collectors.toList());
            if (filterCols.size() == 0) {
                if (value instanceof String) {
                    sql = sql.replace("#" + key + "#", "'" + escapeStr(value.toString()) + "'");
                } else {
                    sql = sql.replace("#" + key + "#", value.toString());
                }
            } else {
                sql = sql.replace("#" + key + "#", "''");
            }
        }
        return sql;
    }

    /**
     * 转义字符处理(['],[&])
     *
     * @param value
     * @return
     */
    public static String escapeStr(String value) {
        //[']处理
        value = value.replace("'", "''");
        //[&]处理
        if (value.toString().contains(CHAR_38)) {
            value = value.replace("&", "'||chr(38)||'");
        }
        return value;
    }

    ;

    /**
     * model中的包含属性，但没有值， 这个属性当没有处理
     *
     * @param sql
     * @param model
     * @return
     */
    public static String getSql(String sql, Map<String, Object> model) {
        if (model == null) {
            model = new HashMap<String, Object>(16);
        }
        ParseXmlUtil util = new ParseXmlUtil();
        List<XmlElement> elementList = util.generateXmlElementList(sql);
        //设置节点是否有效
        for (int i = 0; i < elementList.size(); i++) {
            util.setNodeValid(elementList.get(i), model);
        }
        //设置连接符是否可用
        for (int i = 0; i < elementList.size(); i++) {
            XmlElement xle = elementList.get(i);
            if (xle.getIsValid()) {
                util.setNodePrepend(xle);
            }
        }

        StringBuffer sqlBuffer = new StringBuffer();
        util.parseSql(elementList, sqlBuffer);
        String havaParseSql = replaceSqlValue(sqlBuffer.toString(), model);
        return havaParseSql;
    }

    /**
     * model中的包含属性，但没有值， 这个属性当空传处理
     *
     * @param sql
     * @param model
     * @param attentionEmptyStr
     * @return
     */
    public static String getSql(String sql, HashMap<String, Object> model, Boolean attentionEmptyStr) {
        if (model == null) {
            model = new HashMap<String, Object>(16);
        }
        ParseXmlUtil util = new ParseXmlUtil();
        List<XmlElement> elementList = util.generateXmlElementList(sql);
        //设置节点是否有效
        for (int i = 0; i < elementList.size(); i++) {
            util.setNodeValid(elementList.get(i), model, attentionEmptyStr);
        }
        //设置连接符是否可用
        for (int i = 0; i < elementList.size(); i++) {
            XmlElement xle = elementList.get(i);
            if (xle.getIsValid()) {
                util.setNodePrepend(xle);
            }
        }

        StringBuffer sqlBuffer = new StringBuffer();
        util.parseSql(elementList, sqlBuffer);
        String havaParseSql = replaceSqlValue(sqlBuffer.toString(), model);
        return havaParseSql;
    }

    /**
     * /model中的包含属性，但没有值， 这个属性当空传处理
     *
     * @param sql
     * @param model
     * @param attentionEmptyStr
     * @param clobColList
     * @return
     */
    public static String getSaveOrUpdateSql(String sql, HashMap<String, Object> model,
                                            Boolean attentionEmptyStr, List<ColVo> clobColList) {
        if (model == null) {
            model = new HashMap<String, Object>(16);
        }
        ParseXmlUtil util = new ParseXmlUtil();
        List<XmlElement> elementList = util.generateXmlElementList(sql);
        //设置节点是否有效
        for (int i = 0; i < elementList.size(); i++) {
            util.setNodeValid(elementList.get(i), model, attentionEmptyStr);
        }
        //设置连接符是否可用
        for (int i = 0; i < elementList.size(); i++) {
            XmlElement xle = elementList.get(i);
            if (xle.getIsValid()) {
                util.setNodePrepend(xle);
            }
        }

        StringBuffer sqlBuffer = new StringBuffer();
        util.parseSql(elementList, sqlBuffer);
        String havaParseSql = replaceSaveSqlValue(sqlBuffer.toString(), model, clobColList);
        return havaParseSql;
    }
}