package org.jeecg.modules.camunda.emuns;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */

public enum PropertyType {
    /**
     * IS_COPY_ALLOW:抄送权限
     * AUTH_USERS:抄送对象集合
     * USER_LIST:用户对象集合
     * DEPART_LIST:单位对象集合
     * ROLE_LIST:角色对象集合
     * POSITION_LIST:职位对象集合
     * SUBMIT_USER:提单人
     * START_ERROR:实例启动错误
     * USER_RULE:候选人规则
     * SUBMIT_ORG:提单单位
     * SUBMIT_ROLE:提单角色
     * SUBMIT_POSITION:提单职位
     */
    IS_COPY_ALLOW, AUTH_USERS, USER_LIST, DEPART_LIST, ROLE_LIST, POSITION_LIST, SUBMIT_USER, START_ERROR, USER_RULE,
    SUBMIT_ORG, SUBMIT_ROLE, SUBMIT_POSITION;
    private static final LinkedHashMap<PropertyType, String> NAME_SPACE = new LinkedHashMap<PropertyType, String>();

    static {
        NAME_SPACE.put(IS_COPY_ALLOW, "isCopyAllow");

        NAME_SPACE.put(AUTH_USERS, "authUsers");

        NAME_SPACE.put(USER_LIST, "userList");

        NAME_SPACE.put(DEPART_LIST, "departList");

        NAME_SPACE.put(ROLE_LIST, "roleList");

        NAME_SPACE.put(POSITION_LIST, "positionList");

        NAME_SPACE.put(SUBMIT_USER, "submitUser");

        NAME_SPACE.put(SUBMIT_ORG, "submitOrg");

        NAME_SPACE.put(SUBMIT_ROLE, "submitRole");

        NAME_SPACE.put(SUBMIT_POSITION, "submitPosition");

        NAME_SPACE.put(START_ERROR, "start_error");

        NAME_SPACE.put(USER_RULE, "userRule");

    }

    public String getName() {
        return NAME_SPACE.get(this);
    }
}
