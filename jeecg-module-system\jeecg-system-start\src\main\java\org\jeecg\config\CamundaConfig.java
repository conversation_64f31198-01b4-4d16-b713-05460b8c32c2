package org.jeecg.config;

import org.camunda.bpm.engine.spring.SpringProcessEngineConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * Camunda配置类
 * 解决事务隔离级别检查问题
 */
@Configuration
public class CamundaConfig {

    @Bean
    @Primary
    public SpringProcessEngineConfiguration processEngineConfiguration(DataSource dataSource) {
        SpringProcessEngineConfiguration config = new SpringProcessEngineConfiguration();
        config.setDataSource(dataSource);
        // 跳过事务隔离级别检查
        config.setSkipIsolationLevelCheck(true);
        config.setDatabaseSchemaUpdate("true");
        config.setJobExecutorActivate(false);
        config.setHistory("full");
        return config;
    }
}
