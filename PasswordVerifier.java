import java.security.Key;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.PBEParameterSpec;

/**
 * JeecgBoot Password Verifier
 */
public class PasswordVerifier {

    public static final String ALGORITHM = "PBEWithMD5AndDES";
    private static final int ITERATIONCOUNT = 1000;

    /**
     * Generate PBE key
     */
    private static Key getPbeKey(String password) {
        SecretKeyFactory keyFactory;
        SecretKey secretKey = null;
        try {
            keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
            PBEKeySpec keySpec = new PBEKeySpec(password.toCharArray());
            secretKey = keyFactory.generateSecret(keySpec);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return secretKey;
    }
    
    /**
     * Encrypt plaintext
     */
    public static String encrypt(String plaintext, String password, String salt) {
        Key key = getPbeKey(password);
        byte[] encipheredData = null;
        PBEParameterSpec parameterSpec = new PBEParameterSpec(salt.getBytes(), ITERATIONCOUNT);
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, key, parameterSpec);
            encipheredData = cipher.doFinal(plaintext.getBytes("utf-8"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bytesToHexString(encipheredData);
    }
    
    /**
     * Convert byte array to hex string
     */
    public static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }
    
    public static void main(String[] args) {
        String username = "admin";
        String salt = "c6xUYMqL";
        String encryptedPassword = "f35445bacc284f79";
        
        // Test common passwords
        String[] testPasswords = {
            "123456", "admin", "password", "tld_123456", "admin123",
            "tld_camunda_2024", "jeecg", "jeecg123", "root", "root123",
            "888888", "000000", "111111", "qwerty", "abc123",
            "tld123", "tld_admin", "system", "sa", "test"
        };

        System.out.println("=== JeecgBoot Password Verification ===");
        System.out.println("Username: " + username);
        System.out.println("Salt: " + salt);
        System.out.println("Encrypted password in DB: " + encryptedPassword);
        System.out.println();
        
        for (String testPassword : testPasswords) {
            String encrypted = encrypt(testPassword, username, salt);
            boolean match = encryptedPassword.equals(encrypted);
            System.out.println("Test password: " + testPassword);
            System.out.println("Encrypted result: " + encrypted);
            System.out.println("Match: " + (match ? "YES" : "NO"));
            System.out.println("---");

            if (match) {
                System.out.println("*** FOUND CORRECT PASSWORD! ***");
                System.out.println("Admin login password is: " + testPassword);
                break;
            }
        }
    }
}
