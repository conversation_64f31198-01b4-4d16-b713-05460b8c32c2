package org.jeecg.modules.amis.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 我的工单
 *
 * <AUTHOR>
 */
@Data
public class WorkOrder implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String id;
    private String businessKey;
    private String procInstId;
    private String formId;
    private String formName;
    private String procDefKey;
    private String procDefName;
    private String startUserId;
    private String startUserName;
    private String state;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    private Long duration;
    private String taskId;
    private String firstTask;
}
