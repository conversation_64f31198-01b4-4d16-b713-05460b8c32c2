package org.jeecg.modules.camunda.emuns;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */

public enum TaskUserType {
    /**
     * 任务用户类型
     */
    LIST, SINGLE;
    private static final LinkedHashMap<TaskUserType, String> NAME_SPACE = new LinkedHashMap<TaskUserType, String>();

    static {
        NAME_SPACE.put(LIST, "list");
        NAME_SPACE.put(SING<PERSON>, "user");
    }

    public String getName() {
        return NAME_SPACE.get(this);
    }
}
