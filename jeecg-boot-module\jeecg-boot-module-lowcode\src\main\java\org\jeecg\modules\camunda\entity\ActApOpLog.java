package org.jeecg.modules.camunda.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程历史操作日志表
 *
 * <AUTHOR>
 * @TableName ACT_AP_OP_LOG
 */
@TableName(value = "ACT_AP_OP_LOG")
@Data
public class ActApOpLog implements Serializable {
    /**
     * 日志id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 业务主键
     */
    private String businessKey;

    /**
     * 实例id
     */
    private String procInstId;

    /**
     * 任务节点id
     */
    private String taskId;

    /**
     * 任务节点名称
     */
    private String taskName;
    /**
     * 接收时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;
    /**
     * 操作类型
     */
    private String opType;

    /**
     * 操作描述
     */
    private String opDesc;

    /**
     * 办理意见
     */
    private String opIdea;

    /**
     * 办理时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date opDate;

    /**
     * 办理人id
     */
    private String opUserId;

    /**
     * 办理人
     */
    private String opUser;

    /**
     * 办理人单位id
     */
    private String opOrgId;

    /**
     * 办理人单位
     */
    private String opOrg;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}