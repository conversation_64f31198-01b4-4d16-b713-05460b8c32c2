import java.sql.*;

/**
 * Check admin user information
 */
public class CheckAdminUser {
    
    public static void main(String[] args) {
        String url = "********************************************************************************************************************";
        String username = "root";
        String password = "123456";
        
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection conn = DriverManager.getConnection(url, username, password);
            
            String sql = "SELECT id, username, realname, password, salt, status, del_flag FROM sys_user WHERE username = 'admin'";
            PreparedStatement stmt = conn.prepareStatement(sql);
            ResultSet rs = stmt.executeQuery();
            
            System.out.println("=== Admin User Information ===");
            if (rs.next()) {
                System.out.println("ID: " + rs.getString("id"));
                System.out.println("Username: " + rs.getString("username"));
                System.out.println("Real Name: " + rs.getString("realname"));
                System.out.println("Password (encrypted): " + rs.getString("password"));
                System.out.println("Salt: " + rs.getString("salt"));
                System.out.println("Status: " + rs.getInt("status"));
                System.out.println("Del Flag: " + rs.getInt("del_flag"));
                
                // Check user status
                int status = rs.getInt("status");
                int delFlag = rs.getInt("del_flag");
                
                if (delFlag == 1) {
                    System.out.println("WARNING: User is marked as deleted!");
                }
                if (status != 1) {
                    System.out.println("WARNING: User status is not active (should be 1)!");
                }
            } else {
                System.out.println("ERROR: Admin user not found!");
            }
            
            rs.close();
            stmt.close();
            conn.close();
            
        } catch (Exception e) {
            System.out.println("Database connection error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
