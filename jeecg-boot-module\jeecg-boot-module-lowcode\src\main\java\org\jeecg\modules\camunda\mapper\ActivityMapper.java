package org.jeecg.modules.camunda.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface ActivityMapper {
    /**
     * 删除BPMN流程运行时记录
     *
     * @param id
     */
    void deleteRuExecutionById(@Param("id") String id);

    /**
     * 删除运行时流程人员表
     *
     * @param taskId
     */
    void deleteRuIdentitylinkByTaskId(@Param("taskId") String taskId);

    /**
     * 删除流程运行时变量表
     *
     * @param exId
     */
    void deleteRuVariableByExId(@Param("exId") String exId);

    /**
     * 删除流程运行时任务表
     *
     * @param id
     */
    void deleteRuTaskById(@Param("id") String id);

    /**
     * 根据单位数组获取用户
     *
     * @param list
     * @return
     */
    List<String> queryUserIdsByDepart(List<String> list);

    /**
     * 根据角色数组获取用户
     *
     * @param list
     * @return
     */
    List<String> queryUserIdsByRole(List<String> list);

    /**
     * 根据职位数组获取用户
     *
     * @param list
     * @return
     */
    List<String> queryUserIdsByPost(List<String> list);

    /**
     * 根据提单人单位id获取用户ids
     *
     * @param id
     * @return
     */
    List<String> queryUserIdsByOrgId(@Param("id") String id);

    /**
     * 根据提单人角色id获取用户ids
     *
     * @param id
     * @return
     */
    List<String> queryUserIdsByRoleId(@Param("id") String id);

    /**
     * 根据提单人职位id获取用户ids
     *
     * @param id
     * @return
     */
    List<String> queryUserIdsByPositionId(@Param("id") String id);

    /**
     * 自定义编辑
     *
     * @param sqlStr
     */
    void updateBySql(@Param(value = "sqlStr") String sqlStr);

}
