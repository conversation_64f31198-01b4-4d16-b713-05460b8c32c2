package org.jeecg.modules.camunda.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.camunda.entity.ActApOpLog;
import org.jeecg.modules.camunda.mapper.ActApOpLogMapper;
import org.jeecg.modules.camunda.service.ActApOpLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/act/log")
public class ActApOpLogController {

    @Autowired
    private ActApOpLogMapper actApOpLogMapper;
    @Autowired
    private ActApOpLogService actApOpLogService;

    /**
     * 根据实例获取日志
     *
     * @param actHiProcinst
     * @return
     */
    @AutoLog(value = "流程日志查询-根据实例获取日志")
    @GetMapping(value = "/queryAll")
    public Result<?> queryAll(ActApOpLog actHiProcinst) {
        QueryWrapper<ActApOpLog> queryWrapper = new QueryWrapper<ActApOpLog>();
        queryWrapper.eq("proc_inst_id", actHiProcinst.getProcInstId()).orderByDesc("op_date");
        List<ActApOpLog> logList = actApOpLogMapper.selectList(queryWrapper);
        return Result.OK(logList);
    }
}
