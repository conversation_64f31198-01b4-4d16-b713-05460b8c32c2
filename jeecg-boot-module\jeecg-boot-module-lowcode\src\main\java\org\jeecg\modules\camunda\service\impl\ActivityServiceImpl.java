package org.jeecg.modules.camunda.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.jexl3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.camunda.bpm.engine.*;
import org.camunda.bpm.engine.externaltask.ExternalTask;
import org.camunda.bpm.engine.externaltask.LockedExternalTask;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.runtime.ProcessInstanceModificationBuilder;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.Variables;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.*;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.camunda.bpm.model.xml.instance.DomElement;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.UUIDGenerator;
import org.jeecg.modules.amis.entity.DwfForm;
import org.jeecg.modules.amis.service.DwfFormService;
import org.jeecg.modules.camunda.emuns.*;
import org.jeecg.modules.camunda.entity.ActApOpLog;
import org.jeecg.modules.camunda.mapper.ActivityMapper;
import org.jeecg.modules.camunda.model.TaskModel;
import org.jeecg.modules.camunda.service.ActApOpLogService;
import org.jeecg.modules.camunda.service.ActivityService;
import org.jeecg.modules.camunda.vo.*;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Primary
@Slf4j
public class ActivityServiceImpl implements ActivityService {
    @Resource
    private ExternalTaskService externalTaskService;
    @Resource
    private RuntimeService runtimeService;
    @Resource
    private IdentityService identityService;
    @Resource
    private RepositoryService repositoryService;
    @Resource
    private ActivityMapper activityMapper;
    @Resource
    private ActApOpLogService actApOpLogService;
    private final static String CUSTOM_SIGN = "proc_cus_";
    @Resource
    private DwfFormService dwfFormService;

    /**
     * 1-流程启动并直接提交申请
     *
     * @return
     * @throws IOException
     */
    @Override
    public Result<JSONObject> startAndComplete(ApiVo apiVo) throws IOException {
        Result<JSONObject> result = new Result<JSONObject>();
        String businessKey = StringUtils.isEmpty(apiVo.getBusinessKey()) ? UUIDGenerator.generate() : apiVo.getBusinessKey();
        this.startProcessInstanceByKey(apiVo.getProcessKey(), businessKey, result);
        //如果启动成功
        if (result.isSuccess()) {
            //获取实例
            JSONObject obj = result.getResult();
            String procInstId = (String) obj.get("procInstId");
            //判断是否是动态表单流程
            Map<String, Object> variables = runtimeService.getVariables(procInstId);
            String jsonTableStr = (String) variables.get(VariablesType.PROC_TABLE.getName());
            TableVo tableVo = JSON.parseObject(jsonTableStr, TableVo.class);
            if (tableVo.getIsDynamic() != null && tableVo.getIsDynamic().equals(BooleanType.Y.getName())) {
                //获取动态表单数据
                DwfForm form = dwfFormService.getById(apiVo.getFormId());
                //设置全局变量
                tableVo.setTableKey(form.getTableKey());
                tableVo.setTableName(form.getTableName());
                tableVo.setStateCode(form.getStateCode());
                runtimeService.setVariable(procInstId, VariablesType.PROC_TABLE.getName(), JSON.toJSONString(tableVo));
            } else {
                String updateSql = "update " + tableVo.getTableName() +
                        " set " + tableVo.getStateCode() + " ='C' where " + tableVo.getTableKey() + "='" + businessKey + "'";
                activityMapper.updateBySql(updateSql);
            }
            //申请节点直接提交（默认第一个用户节点）
            ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
            TaskService taskService = processEngine.getTaskService();
            Task applyTask = taskService.createTaskQuery().processInstanceId(procInstId).active().list().get(0);
            //如果带参数
            if (apiVo.getVariables() != null && apiVo.getVariables().size() > 0) {
                apiVo.getVariables().stream().forEach(variable -> runtimeService.setVariable(applyTask.getExecutionId(), variable.getKey(), variable.getValue()));
            }
            //下一节点走网关，并且指定审核人，不指定节点
            if (StringUtils.isNotEmpty(apiVo.getNextUserIds())) {
                List<String> nextUserlist = Arrays.asList(apiVo.getNextUserIds().split(","));
                //获取下一节点key
                String nexTaskKey = this.getNextFlowNode(procInstId, applyTask.getTaskDefinitionKey());
                Object userObj = variables.get(nexTaskKey + "_" + TaskUserType.LIST.getName());
                if (userObj != null) {
                    //多实例
                    runtimeService.setVariable(procInstId, nexTaskKey + "_" + TaskUserType.LIST.getName(), nextUserlist);
                } else {
                    //单实例
                    runtimeService.setVariable(procInstId, nexTaskKey + "_" + TaskUserType.SINGLE.getName(), nextUserlist.get(0));
                }
            }
            //操作日志保存
            ActApOpLog opLog = new ActApOpLog();
            opLog.setProcInstId(procInstId);
            opLog.setTaskId(applyTask.getId());
            opLog.setTaskName(applyTask.getName());
            opLog.setOpType(OperateType.SUBMIT.getName());
            opLog.setOpDesc("提交");
            opLog.setStartDate(applyTask.getCreateTime());
            actApOpLogService.addOpLog(opLog);
            //提交
            taskService.complete(applyTask.getId());
        }
        return result;
    }

    /**
     * 完成待办
     *
     * @param apiVo
     */
    @Override
    public void complete(ApiVo apiVo) {
        //获取跳转节点的执行候选人
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        TaskService taskService = processEngine.getTaskService();
        Task task = taskService.createTaskQuery().taskId(apiVo.getTaskId()).singleResult();
        String procInstId = task.getProcessInstanceId();
        //如果带参数
        if (apiVo.getVariables() != null && apiVo.getVariables().size() > 0) {
            apiVo.getVariables().stream().forEach(variable -> runtimeService.setVariable(task.getExecutionId(), variable.getKey(), variable.getValue()));
        }
        //下一节点走网关，并且指定审核人，不指定节点
        if (StringUtils.isNotEmpty(apiVo.getNextUserIds())) {
            List<String> nextUserlist = Arrays.asList(apiVo.getNextUserIds().split(","));
            //获取下一节点key
            String nexTaskKey = this.getNextFlowNode(procInstId, task.getTaskDefinitionKey());
            Map<String, Object> variables = runtimeService.getVariables(procInstId);
            Object userObj = variables.get(nexTaskKey + "_" + TaskUserType.LIST.getName());
            if (userObj != null) {
                //多实例
                runtimeService.setVariable(procInstId, nexTaskKey + "_" + TaskUserType.LIST.getName(), nextUserlist);
            } else {
                //单实例
                runtimeService.setVariable(procInstId, nexTaskKey + "_" + TaskUserType.SINGLE.getName(), nextUserlist.get(0));
            }
        }
        //操作日志保存
        ActApOpLog opLog = new ActApOpLog();
        opLog.setProcInstId(procInstId);
        opLog.setTaskId(task.getId());
        opLog.setTaskName(task.getName());
        opLog.setOpType(apiVo.getOpType());
        opLog.setOpDesc(apiVo.getOpDesc());
        opLog.setOpIdea(apiVo.getComment());
        opLog.setStartDate(task.getCreateTime());
        actApOpLogService.addOpLog(opLog);
        //提交
        taskService.complete(apiVo.getTaskId());
    }

    /**
     * 外部任务完成待办
     *
     * @param apiVo
     */
    @Override
    public void externalComplete(ApiVo apiVo) {
        List<ExternalTask> activeTaskList = externalTaskService.createExternalTaskQuery()
                .activityId(apiVo.getTaskKey())
                .processInstanceId(apiVo.getProcInstId())
                .active().list();
        for (ExternalTask task : activeTaskList) {
            List<LockedExternalTask> lockTaskList = externalTaskService.fetchAndLock(1, "java-client")
                    .topic(task.getTopicName(), 1000L)
                    .businessKey(task.getBusinessKey())
                    .processDefinitionKey(task.getProcessDefinitionKey())
                    .execute();
            for (LockedExternalTask lockTask : lockTaskList) {
                // 如果执行成功，则设置任务成功
                externalTaskService.complete(lockTask.getId(), "java-client");
            }
        }
    }

    /**
     * 流程实例启动
     *
     * @param processKey
     * @param businessKey
     */
    @Override
    public Result<JSONObject> startProcessInstanceByKey(String processKey, String businessKey, Result<JSONObject> result) throws IOException {
        JSONObject obj = new JSONObject();
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(processKey).latestVersion().singleResult();
        String processDefinitionId = processDefinition.getId();
        InputStream processModel = ProcessEngines
                .getDefaultProcessEngine()
                .getRepositoryService()
                .getProcessModel(processDefinitionId);
        BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(processModel);
        processModel.close();
        VariableMap variables = Variables.createVariables();
        //处理流程参数
        variables = this.opProcVar(variables, bpmnModelInstance);
        //所有用户任务参数
        variables = this.opUserTaskVar(variables, bpmnModelInstance);
        //设置开启人变量
        TaskUserVo userVo = new TaskUserVo();
        userVo.setId(this.getLoginUser().getId());
        userVo.setRealName(this.getLoginUser().getRealname());
        variables.put(VariablesType.START_USER.getName(), JSON.toJSONString(userVo));
        //错误统计
        if (variables.get(PropertyType.START_ERROR.getName()) != null) {
            obj.put("errorList", (List<String>) variables.get(PropertyType.START_ERROR.getName()));
            result.setResult(obj);
            result.error500("启动失败，请联系系统管理员！");
            return result;
        }
        //启动
        identityService.setAuthenticatedUserId(this.getLoginUser().getId());
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processKey, businessKey, variables);
        obj.put("procInstId", processInstance.getId());
        obj.put("businessKey", businessKey);
        result.setResult(obj);
        result.success("启动成功！");
        return result;
    }

    /**
     * 处理流程参数
     *
     * @param variables
     */
    private VariableMap opProcVar(VariableMap variables, BpmnModelInstance bpmnModelInstance) {
        //流程
        Collection<Process> procList = bpmnModelInstance.getModelElementsByType(Process.class);
        for (Process process : procList) {
            ExtensionElements extensionElements = process.getExtensionElements();
            //如果有扩展属性
            if (extensionElements != null) {
                Collection<CamundaProperty> properties = extensionElements.getElementsQuery()
                        .filterByType(CamundaProperties.class)
                        .singleResult()
                        .getCamundaProperties();
                List<CamundaProperty> customProps = properties.stream().filter(node -> node.getCamundaName().contains(CUSTOM_SIGN)).collect(Collectors.toList());
                //获取所有自定义扩展的属性
                for (CamundaProperty property : customProps) {
                    //执行候选人配置信息
                    if (property.getCamundaName().equals(CustomType.PROC_CUS_COPY.getName())) {
                        variables = this.opCopyAllow(variables, property.getCamundaValue(), customProps);
                    }
                    //数据表配置信息解析
                    if (property.getCamundaName().equals(CustomType.PROC_TABLE.getName())) {
                        variables = this.opProcTable(variables, property.getCamundaValue());
                    }
                }
            }
        }
        return variables;
    }

    /**
     * 处理用户任务参数
     *
     * @param variables
     */
    private VariableMap opUserTaskVar(VariableMap variables, BpmnModelInstance bpmnModelInstance) {
        Collection<UserTask> userTaskList = bpmnModelInstance.getModelElementsByType(UserTask.class);
        for (UserTask userTask : userTaskList) {
            DomElement dom = userTask.getDomElement();
            List<DomElement> childNodeList = dom.getChildElements();
            List<DomElement> multiElemList = childNodeList.stream().filter(node -> node.getLocalName().equals(DomType.MULTI.getName())).collect(Collectors.toList());
            //或签、会签用户任务--动态设置执行候选人
            ExtensionElements extensionElements = userTask.getExtensionElements();
            //如果有扩展属性
            if (extensionElements != null) {
                Collection<CamundaProperty> properties = extensionElements.getElementsQuery()
                        .filterByType(CamundaProperties.class)
                        .singleResult()
                        .getCamundaProperties();
                List<CamundaProperty> customProps = properties.stream().filter(node -> node.getCamundaName().contains(CUSTOM_SIGN)).collect(Collectors.toList());
                //获取所有自定义扩展的属性
                for (CamundaProperty property : customProps) {
                    //执行候选人配置信息
                    if (property.getCamundaName().equals(CustomType.TASK_CUS_USERS.getName())) {
                        JSONObject jsonObject = JSONObject.parseObject(property.getCamundaValue());
                        JSONObject authUsers = (JSONObject) jsonObject.get(PropertyType.AUTH_USERS.getName());
                        //候选人
                        String userRule = jsonObject.get(PropertyType.USER_RULE.getName()).toString();
                        List<String> authUserList = this.getUserIdList(authUsers, userRule, customProps);
                        if (multiElemList.size() > 0) {
                            if (authUserList.size() > 0) {
                                variables.put(userTask.getId() + "_" + TaskUserType.LIST.getName(), authUserList);
                            } else {
                                this.recordError(variables, "【" + userTask.getName() + "】节点中办理人员为空！");
                            }
                        } else {
                            variables.put(userTask.getId() + "_" + TaskUserType.SINGLE.getName(), authUserList.get(0));
                        }
                    } else if (property.getCamundaName().equals(CustomType.TASK_CUS_BTNS.getName())) {
                        //执行候用户节点按钮权限信息
                        variables = this.opProcTaskButton(userTask.getId(), variables, property.getCamundaValue());
                    } else if (property.getCamundaName().equals(CustomType.TASK_CUS_VARS.getName())) {
                        //执行候用户节点按钮权限信息
                        variables = this.opProcTaskVars(userTask.getId(), variables, property.getCamundaValue());
                    }
                }
            }
        }
        return variables;
    }

    /**
     * 处理用户节点表单参数
     *
     * @param taskKey
     * @param variables
     * @param camundaValue
     * @return
     */
    private VariableMap opProcTaskVars(String taskKey, VariableMap variables, String camundaValue) {
        variables.put(taskKey + "_form_var", camundaValue);
        return variables;
    }

    /**
     * 处理用户节点操作按钮权限
     *
     * @param taskKey
     * @param variables
     * @param camundaValue
     * @return
     */
    private VariableMap opProcTaskButton(String taskKey, VariableMap variables, String camundaValue) {
        ButtonVo btnVo = JSONObject.parseObject(camundaValue, ButtonVo.class);
        variables.put(taskKey + "_btn_auth", JSON.toJSONString(btnVo));
        return variables;
    }

    /**
     * 处理流程关联表权限
     *
     * @param variables
     * @param camundaValue
     */
    private VariableMap opProcTable(VariableMap variables, String camundaValue) {
        TableVo tableVo = JSONObject.parseObject(camundaValue, TableVo.class);
        variables.put(VariablesType.PROC_TABLE.getName(), JSON.toJSONString(tableVo));
        return variables;
    }

    /**
     * 处理抄送权限
     *
     * @param variables
     * @param camundaValue
     */
    private VariableMap opCopyAllow(VariableMap variables, String camundaValue, List<CamundaProperty> customProps) {
        JSONObject procCopy = JSONObject.parseObject(camundaValue);
        //如果有抄送权限
        if (procCopy.get(PropertyType.IS_COPY_ALLOW.getName()).toString().equals(BooleanType.Y.getName())) {
            JSONObject authUsers = (JSONObject) procCopy.get(PropertyType.AUTH_USERS.getName());
            String userRule = procCopy.get(PropertyType.USER_RULE.getName()).toString();
            //候选人
            List<String> authUserList = this.getUserIdList(authUsers, userRule, customProps);
            variables.put(VariablesType.COPY_USERS.getName(), authUserList);
        }
        return variables;
    }

    /**
     * 根据配置的人员、单位、角色、职务返回所有候选人数组
     *
     * @param authUsers
     * @return
     */
    private List<String> getUserIdList(JSONObject authUsers, String candRule, List<CamundaProperty> customProps) {
        LoginUser sysUser = this.getLoginUser();
        //初始化候选人数组--并集去重
        List<String> authUserList = new ArrayList<String>();
        //交集
        HashMap<String, List<String>> map = new HashMap<String, List<String>>(16);
        //根据选定人添加执行候选人
        JSONArray userJosnArr = authUsers.getJSONArray(PropertyType.USER_LIST.getName());
        List<String> userList = userJosnArr != null ? userJosnArr.toJavaList(String.class) : new ArrayList<String>();
        if (userList.size() > 0) {
            if (candRule.equals(CandRuleType.UNION.getName())) {
                authUserList.addAll(userList);
            } else {
                map.put(PropertyType.USER_LIST.getName(), userList);
            }

        }
        //根据选定单位添加执行候选人
        JSONArray departJosnArr = authUsers.getJSONArray(PropertyType.DEPART_LIST.getName());
        List<String> departList = departJosnArr != null ? departJosnArr.toJavaList(String.class) : new ArrayList<String>();
        if (departList.size() > 0) {
            List<String> userIdList = activityMapper.queryUserIdsByDepart(departList);
            if (candRule.equals(CandRuleType.UNION.getName())) {
                authUserList.addAll(userIdList);
            } else {
                map.put(PropertyType.DEPART_LIST.getName(), userIdList);
            }
        }
        //根据选定角色添加执行候选人
        JSONArray roleJosnArr = authUsers.getJSONArray(PropertyType.ROLE_LIST.getName());
        List<String> roleList = roleJosnArr != null ? roleJosnArr.toJavaList(String.class) : new ArrayList<String>();
        if (roleList.size() > 0) {
            List<String> userIdList = activityMapper.queryUserIdsByRole(roleList);
            if (candRule.equals(CandRuleType.UNION.getName())) {
                authUserList.addAll(userIdList);
            } else {
                map.put(PropertyType.ROLE_LIST.getName(), userIdList);
            }
        }
        //根据选定职务添加执行候选人
        JSONArray positionJosnArr = authUsers.getJSONArray(PropertyType.POSITION_LIST.getName());
        List<String> positionList = positionJosnArr != null ? positionJosnArr.toJavaList(String.class) : new ArrayList<String>();
        if (positionList.size() > 0) {
            List<String> userIdList = activityMapper.queryUserIdsByPost(positionList);
            if (candRule.equals(CandRuleType.UNION.getName())) {
                authUserList.addAll(userIdList);
            } else {
                map.put(PropertyType.POSITION_LIST.getName(), userIdList);
            }
        }
        //提单人
        if (authUsers.get(PropertyType.SUBMIT_USER.getName()) != null) {
            if (authUsers.get(PropertyType.SUBMIT_USER.getName()).equals(BooleanType.Y.getName())) {
                List<String> userIdList = new ArrayList<String>();
                userIdList.add(sysUser.getId());
                if (candRule.equals(CandRuleType.UNION.getName())) {
                    authUserList.addAll(userIdList);
                } else {
                    map.put(PropertyType.SUBMIT_USER.getName(), userIdList);
                }
            }
        }
        //提单单位
        if (authUsers.get(PropertyType.SUBMIT_ORG.getName()) != null) {
            if (authUsers.get(PropertyType.SUBMIT_ORG.getName()).equals(BooleanType.Y.getName())) {
                List<String> userIdList = activityMapper.queryUserIdsByOrgId(sysUser.getId());
                if (candRule.equals(CandRuleType.UNION.getName())) {
                    authUserList.addAll(userIdList);
                } else {
                    map.put(PropertyType.SUBMIT_ORG.getName(), userIdList);
                }
            }
        }
        //提单角色
        if (authUsers.get(PropertyType.SUBMIT_ROLE.getName()) != null) {
            if (authUsers.get(PropertyType.SUBMIT_ROLE.getName()).equals(BooleanType.Y.getName())) {
                List<String> userIdList = activityMapper.queryUserIdsByRoleId(sysUser.getId());
                if (candRule.equals(CandRuleType.UNION.getName())) {
                    authUserList.addAll(userIdList);
                } else {
                    map.put(PropertyType.SUBMIT_ROLE.getName(), userIdList);
                }
            }
        }
        //提单职位
        if (StringUtils.isNotEmpty(sysUser.getPost())) {
            if (authUsers.get(PropertyType.SUBMIT_POSITION.getName()) != null) {
                if (authUsers.get(PropertyType.SUBMIT_POSITION.getName()).equals(BooleanType.Y.getName())) {
                    List<String> userIdList = activityMapper.queryUserIdsByPositionId(sysUser.getPost());
                    if (candRule.equals(CandRuleType.UNION.getName())) {
                        authUserList.addAll(userIdList);
                    } else {
                        map.put(PropertyType.SUBMIT_POSITION.getName(), userIdList);
                    }
                }
            }
        }
        if (candRule.equals(CandRuleType.UNION.getName())) {
            //并集去重处理
            authUserList = authUserList.stream().distinct().collect(Collectors.toList());
            return authUserList;
        } else {
            // 遍历键值对
            List<String> interList = new ArrayList<String>();
            boolean start = false;
            for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                List<String> values = entry.getValue();
                if (!start) {
                    interList.addAll(values);
                    start = true;
                } else {
                    interList.retainAll(values);
                }
            }
            return interList;
        }
    }

    /**
     * 记录启动过程中错误
     *
     * @param variables
     * @return
     */
    private VariableMap recordError(VariableMap variables, String errorMsg) {
        List<String> errorList = new ArrayList<String>();
        if (variables.get(PropertyType.START_ERROR.getName()) != null) {
            errorList = (List<String>) variables.get(PropertyType.START_ERROR.getName());
        }
        errorList.add(errorMsg);
        variables.put(PropertyType.START_ERROR.getName(), errorList);
        return variables;

    }

    /**
     * 删除任务
     *
     * @param targetTask
     */
    @Override
    public void deleteTask(Task targetTask) {
        activityMapper.deleteRuIdentitylinkByTaskId(targetTask.getId());
        activityMapper.deleteRuVariableByExId(targetTask.getExecutionId());
        activityMapper.deleteRuTaskById(targetTask.getId());
        activityMapper.deleteRuExecutionById(targetTask.getExecutionId());
    }

    /**
     * 流程操作日志添加
     *
     * @param processInstanceModificationBuilder
     * @param opLogVo
     */
    @Override
    public void addOpLog(ProcessInstanceModificationBuilder processInstanceModificationBuilder, OpLogVo opLogVo) {
        LoginUser sysUser = this.getLoginUser();
        opLogVo.setUserId(sysUser.getId());
        opLogVo.setRealName(sysUser.getRealname());
        String annotation = JSON.toJSONString(opLogVo);
        //这设置的审批人及查询意见时的userId
        identityService.setAuthenticatedUserId(sysUser.getId());
        processInstanceModificationBuilder.setAnnotation(annotation).execute();
    }

    /**
     * 获取任务节点配置信息
     *
     * @param procInsId
     * @param taskKey
     * @return
     */
    @Override
    public UserTask getUerTask(String procInsId, String taskKey) {
        try {
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(procInsId)
                    .singleResult();
            InputStream processModel = ProcessEngines
                    .getDefaultProcessEngine()
                    .getRepositoryService()
                    .getProcessModel(processInstance.getProcessDefinitionId());
            BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(processModel);
            UserTask userTask = bpmnModelInstance.getModelElementById(taskKey);
            processModel.close();
            return userTask;
        } catch (IOException e) {
            return null;
        }
    }

    /**
     * 判断任务节点是不是第一个任务节点
     *
     * @param procInsId
     * @param taskKey
     * @return
     */
    @Override
    public boolean isFirstTask(String procInsId, String taskKey) {
        try {
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(procInsId)
                    .singleResult();
            InputStream processModel = ProcessEngines
                    .getDefaultProcessEngine()
                    .getRepositoryService()
                    .getProcessModel(processInstance.getProcessDefinitionId());
            BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(processModel);
            processModel.close();
            Collection<UserTask> userTaskList = bpmnModelInstance.getModelElementsByType(UserTask.class);
            UserTask firstTask = userTaskList.stream().findFirst().get();
            return taskKey.equals(firstTask.getId());
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 获取节点
     *
     * @param procInsId
     * @param taskKey
     * @return
     */
    @Override
    public FlowNode getFlowNode(String procInsId, String taskKey) throws IOException {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(procInsId)
                .singleResult();
        InputStream processModel = ProcessEngines
                .getDefaultProcessEngine()
                .getRepositoryService()
                .getProcessModel(processInstance.getProcessDefinitionId());
        BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(processModel);
        processModel.close();
        return bpmnModelInstance.getModelElementById(taskKey);
    }

    /**
     * 获取当前登录用户
     */
    public LoginUser getLoginUser() {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser == null) {
            LoginUser defualtUser = new LoginUser();
            defualtUser.setId("a75d45a015c44384a04449ee80dc3503");
            defualtUser.setRealname("jeecg");
            defualtUser.setPost("1752159911509655554");
            return defualtUser;
        } else {
            return sysUser;
        }

    }

    /**
     * 获取bpmn流程图
     *
     * @param procInsId
     * @return
     */
    private BpmnModelInstance getBpmnModelInstance(String procInsId) {
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        // 获取 HistoryService 对象
        HistoryService historyService = processEngine.getHistoryService();
        // 创建 HistoricProcessInstanceQuery 查询对象，设置查询条件，获取唯一结果
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(procInsId)
                .singleResult();
        // 获取 historicProcessInstance 对应的 processDefinitionId 和 processDefinitionVersion
        String processDefinitionId = historicProcessInstance.getProcessDefinitionId();
        BpmnModelInstance modelInstance = repositoryService.getBpmnModelInstance(processDefinitionId);
        return modelInstance;
    }

    /**
     * 根据实例获取任务节点状态
     *
     * @param procInsId
     * @return
     */
    @Override
    public List<TaskModel> getTaskListByProcInst(String procInsId) {
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        // 获取 HistoryService 对象
        HistoryService historyService = processEngine.getHistoryService();
        BpmnModelInstance modelInstance = this.getBpmnModelInstance(procInsId);
        //1、查询所有待办任务
        List<HistoricActivityInstance> activeHisActInstList = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(procInsId)
                .unfinished()
                .orderByHistoricActivityInstanceStartTime().desc().list();
        List<String> activeTaskKeyList = new ArrayList<String>();
        for (HistoricActivityInstance actInstList : activeHisActInstList) {
            if (!actInstList.getActivityType().equals(DomType.BODY.getName())) {
                activeTaskKeyList.add(actInstList.getActivityId());
            }
        }
        //2、查询已完成节点实例集合
        List<HistoricActivityInstance> hisActInstList = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(procInsId)
                .finished()
                .orderByHistoricActivityInstanceEndTime().desc().list();
        //3、所有待办任务之前的节点
        List<String> beforeTaskKeyList = new ArrayList<String>();
        for (String activeTaskKey : activeTaskKeyList) {
            List<String> predecessors = new ArrayList<>();
            // 记录已经访问过的节点
            List<FlowNode> visitedNodes = new ArrayList<>();
            List<String> nextdecessors = new ArrayList<>();
            // 记录已经访问过的节点
            List<FlowNode> nextVisitedNodes = new ArrayList<>();
            FlowNode targetNode = modelInstance.getModelElementById(activeTaskKey);
            if (targetNode != null) {
                findPredecessors(targetNode, predecessors, visitedNodes);
                findNextdecessors(targetNode, nextdecessors, nextVisitedNodes);
                // 删除 a 中与 b 中相同的字符串
                predecessors.removeIf(nextdecessors::contains);
                beforeTaskKeyList.addAll(predecessors);
            }

        }
        // 将列表中的元素放入 HashSet 集合中去重
        Set<String> set = new HashSet<>(beforeTaskKeyList);
        // 将去重后的元素放回列表中
        beforeTaskKeyList.clear();
        beforeTaskKeyList.addAll(set);
        //4、过滤出所有已完成的节点
        List<String> completeTaskKeyList = new ArrayList<String>();
        //如果流程已完成
        if (activeTaskKeyList.size() == 0) {
            beforeTaskKeyList.clear();
            Collection<FlowNode> elements = modelInstance.getModelElementsByType(FlowNode.class);
            for (FlowNode element : elements) {
                beforeTaskKeyList.add(element.getId());
            }
        }
        for (String taskKey : beforeTaskKeyList) {
            for (HistoricActivityInstance hisActInst : hisActInstList) {
                if (taskKey.equals(hisActInst.getActivityId())) {
                    if (completeTaskKeyList.contains(taskKey)) {
                        break;
                    }
                    completeTaskKeyList.add(hisActInst.getActivityId());
                }
            }
        }
        List<TaskModel> taskModelList = new ArrayList<TaskModel>();
        for (String fKey : completeTaskKeyList) {
            TaskModel taskModel = new TaskModel();
            taskModel.setTaskKey(fKey);
            taskModel.setState(TaskState.COMPLETED.getName());
            taskModelList.add(taskModel);
        }
        for (String aKey : activeTaskKeyList) {
            TaskModel taskModel = new TaskModel();
            taskModel.setTaskKey(aKey);
            taskModel.setState(TaskState.ACTIVE.getName());
            taskModelList.add(taskModel);
        }
        return taskModelList;
    }

    /**
     * 查询某节点之前所有节点
     *
     * @param node
     * @param predecessors
     * @param visitedNodes
     */
    private void findPredecessors(FlowNode node, List<String> predecessors, List<FlowNode> visitedNodes) {
        // 已经访问过该节点，避免出现无限递归情况
        if (visitedNodes.contains(node)) {
            return;
        }
        visitedNodes.add(node);
        for (SequenceFlow incomingFlow : node.getIncoming()) {
            FlowNode sourceNode = (FlowNode) incomingFlow.getSource();
            predecessors.add(sourceNode.getId());
            // 递归查找前驱节点
            findPredecessors(sourceNode, predecessors, visitedNodes);
        }
    }

    /**
     * 查询某节点之后所有节点
     *
     * @param node
     * @param nextdecessors
     * @param nextVisitedNodes
     */
    private void findNextdecessors(FlowNode node, List<String> nextdecessors, List<FlowNode> nextVisitedNodes) {
        // 已经访问过该节点，避免出现无限递归情况
        if (nextVisitedNodes.contains(node)) {
            return;
        }
        nextVisitedNodes.add(node);
        for (SequenceFlow outgoingFlow : node.getOutgoing()) {
            FlowNode targetNode = (FlowNode) outgoingFlow.getTarget();
            nextdecessors.add(targetNode.getId());
            // 递归查找前驱节点
            findNextdecessors(targetNode, nextdecessors, nextVisitedNodes);
        }
    }

    /**
     * 查询某节点之前所有节点
     *
     * @param node
     * @param predecessors
     * @param visitedNodes
     */
    @Override
    public void findPreFlowList(FlowNode node, List<FlowNode> predecessors, List<FlowNode> visitedNodes) {
        // 已经访问过该节点，避免出现无限递归情况
        if (visitedNodes.contains(node)) {
            return;
        }
        visitedNodes.add(node);
        for (SequenceFlow incomingFlow : node.getIncoming()) {
            FlowNode sourceNode = (FlowNode) incomingFlow.getSource();
            predecessors.add(sourceNode);
            // 递归查找前驱节点
            findPreFlowList(sourceNode, predecessors, visitedNodes);
        }
    }

    /**
     * 根据实例id和当前节点，获取下一节点的FlowNode
     *
     * @param procInsId
     * @param currTaskKey
     * @return
     */
    @Override
    public String getNextFlowNode(String procInsId, String currTaskKey) {
        BpmnModelInstance modelInstance = this.getBpmnModelInstance(procInsId);
        FlowNode currNode = modelInstance.getModelElementById(currTaskKey);
        FlowNode nextNode = null;
        String taskKey = null;
        for (SequenceFlow outgoingFlow : currNode.getOutgoing()) {
            nextNode = (FlowNode) outgoingFlow.getTarget();
        }
        if (DomType.USER_TASK.getName().equals(nextNode.getElementType().getTypeName())) {
            taskKey = nextNode.getId();
        } else if (DomType.GATEWAY.getName().equals(nextNode.getElementType().getTypeName())) {
            taskKey = getConditionNode(procInsId, nextNode).getId();
        }
        return taskKey;
    }

    public FlowNode getConditionNode(String procInsId, FlowNode gatewayNode) {
        FlowNode nextNode = null;
        Map<String, Object> variables = runtimeService.getVariables(procInsId);
        for (SequenceFlow outgoingFlow : gatewayNode.getOutgoing()) {
            ConditionExpression conditionExpression = outgoingFlow.getConditionExpression();
            int startIndex = conditionExpression.getTextContent().indexOf("${");
            int endIndex = conditionExpression.getTextContent().indexOf("}");
            String expression = conditionExpression.getTextContent().substring(startIndex + 2, endIndex);
            JexlEngine jexlEngine = new JexlBuilder().create();
            JexlExpression jexlExpression2 = jexlEngine.createExpression(expression);
            JexlContext jexlContext = new MapContext();
            for (String key : variables.keySet()) {
                Object value = variables.get(key);
                jexlContext.set(key, value);
            }
            if ((boolean) jexlExpression2.evaluate(jexlContext)) {
                nextNode = (FlowNode) outgoingFlow.getTarget();
                break;
            }
        }
        return nextNode;
    }
}
