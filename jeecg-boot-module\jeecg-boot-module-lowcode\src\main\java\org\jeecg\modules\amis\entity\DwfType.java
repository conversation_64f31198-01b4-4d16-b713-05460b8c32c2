package org.jeecg.modules.amis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 动态表单业务类型表
 *
 * <AUTHOR>
 * @TableName dwf_type
 */
@TableName(value = "dwf_type")
@Data
public class DwfType implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 分类id;主键
     */
    @TableId
    private String id;

    /**
     * 分类编码
     */
    private String typeCode;

    /**
     * 分类名称
     */
    private String typeName;

    /**
     * 状态
     */
    private String state;

    /**
     * 序号
     */
    private Integer seq;

    @TableField(exist = false)
    private List<DwfForm> formList;

}