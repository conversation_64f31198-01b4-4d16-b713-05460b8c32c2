package org.jeecg.modules.api.interceptor;

import lombok.SneakyThrows;
import org.jeecg.common.util.encryption.AesEncryptUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.ssssssss.magicapi.core.context.MagicUser;
import org.ssssssss.magicapi.core.exception.MagicLoginException;
import org.ssssssss.magicapi.core.interceptor.Authorization;
import org.ssssssss.magicapi.core.interceptor.AuthorizationInterceptor;
import org.ssssssss.magicapi.core.servlet.MagicHttpServletRequest;
import org.ssssssss.magicapi.utils.MD5Utils;

/**
 * 自定义UI界面鉴权
 * https://ssssssss.org/magic-api/pages/security/operation/
 * <AUTHOR>
 * @see org.ssssssss.magicapi.core.interceptor.AuthorizationInterceptor
 */
@Component
public class CustomUIAuthorizationInterceptor implements AuthorizationInterceptor {


    @Value(value = "${magic-api.security.username}")
    private String loginName;
    @Value(value = "${magic-api.security.password}")
    private String loginPass;
    /**
     * 配置UI是否需要登录
     */
    @Override
    public boolean requireLogin() {
        return true;
    }

    /**
     * 自定义登录方法
     *  @param username 用户名
     * @param password 密码
     * @return
     */
    @SneakyThrows
    @Override
    public MagicUser login(String username, String password) throws MagicLoginException {
        password = AesEncryptUtil.desEncrypt(password.replaceAll("%2B", "\\+")).trim();
        if (loginName.equals(username) && loginPass.equals(password)) {
            // 登录成功后 构造MagicUser对象。
            String token = MD5Utils.encrypt(String.format("%s||%s", username, password));
            System.out.println(token);
            return new MagicUser(loginName, loginName, token);
        }
        throw new MagicLoginException("用户名或密码不正确");
    }

    /**
     * 根据Token获取用户信息
     */
    @Override
    public MagicUser getUserByToken(String token) throws MagicLoginException {
        String validToken = MD5Utils.encrypt(String.format("%s||%s", loginName, loginPass));
        if (validToken.equals(token)) {
            return new MagicUser(loginName, loginName, token);
        }
        throw new MagicLoginException("token无效");
    }

    /**
     * 是否允许访问
     * @param magicUser	用户信息
     * @return
     */
    @Override
    public boolean allowVisit(MagicUser magicUser, MagicHttpServletRequest request, Authorization authorization) {
        if(authorization == Authorization.DELETE || authorization == Authorization.UPLOAD){
            // 禁止上传和删除
            return false;
        }
        return true;
    }
}
