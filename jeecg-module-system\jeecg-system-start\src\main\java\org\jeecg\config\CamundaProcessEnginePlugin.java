package org.jeecg.config;

import org.camunda.bpm.engine.ProcessEngine;
import org.camunda.bpm.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.camunda.bpm.engine.impl.cfg.ProcessEnginePlugin;
import org.springframework.stereotype.Component;

/**
 * Camunda流程引擎插件
 * 用于设置跳过事务隔离级别检查
 */
@Component
public class CamundaProcessEnginePlugin implements ProcessEnginePlugin {

    @Override
    public void preInit(ProcessEngineConfigurationImpl processEngineConfiguration) {
        // 在流程引擎初始化前设置跳过事务隔离级别检查
        processEngineConfiguration.setSkipIsolationLevelCheck(true);
    }

    @Override
    public void postInit(ProcessEngineConfigurationImpl processEngineConfiguration) {
        // 流程引擎初始化后的处理
    }

    @Override
    public void postProcessEngineBuild(ProcessEngine processEngine) {
        // 流程引擎构建完成后的处理
    }
}
