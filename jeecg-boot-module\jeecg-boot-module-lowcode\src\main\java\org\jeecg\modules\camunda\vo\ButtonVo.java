package org.jeecg.modules.camunda.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 操作按钮权限
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ButtonVo {
    private static final long serialVersionUID = 1L;
    /**
     * 提交
     */
    private String isSubmit;
    /**
     * 通过
     */
    private String isComplete;
    /**
     * 驳回
     */
    private String isReject;
    /**
     * 退回至任意节点
     */
    private String isBackAny;
}
