package org.jeecg.modules.amis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 低代码页面表
 *
 * <AUTHOR>
 * @TableName lcd_page
 */
@TableName(value = "lcd_page")
@Data
public class LcdPage implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId
    @Schema(description = "主键id")
    private String id;

    /**
     * 模块id
     */
    @Schema(description = "模块id")
    private String moduleId;

    /**
     * 页面名称
     */
    @Schema(description = "页面名称")
    private String pageName;

    /**
     * JSON数据代码
     */
    @Schema(description = "JSON数据代码")
    private String jsonSchema;

    /**
     * 页面描述
     */
    @Schema(description = "页面描述")
    private String description;

    /**
     * 序号
     */
    @Schema(description = "序号")
    private Integer seq;

    /**
     * 删除状态（0正常，1已删除）
     */
    @Schema(description = "删除状态（0正常，1已删除）")
    private String delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;


}