package org.jeecg.modules.amis.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.amis.entity.DwfForm;
import org.jeecg.modules.amis.entity.DwfType;
import org.jeecg.modules.amis.mapper.DwfFormMapper;
import org.jeecg.modules.amis.model.TaskOrder;
import org.jeecg.modules.amis.model.WorkOrder;
import org.jeecg.modules.amis.service.DwfFormService;
import org.jeecg.modules.amis.service.DwfTypeService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 动态表单
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/dwf/form")
public class DwfFormController {

    @Resource
    private DwfFormService dwfFormService;
    @Resource
    private DwfFormMapper dwfFormMapper;
    @Resource
    private DwfTypeService dwfTypeService;

    /**
     * 分页列表查询
     *
     * @param dwfForm
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "动态表单管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> list(DwfForm dwfForm, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                          HttpServletRequest req) {
        QueryWrapper<DwfForm> queryWrapper = QueryGenerator.initQueryWrapper(dwfForm, req.getParameterMap());
        queryWrapper.orderByAsc("seq");
        Page<DwfForm> page = new Page<DwfForm>(pageNo, pageSize);
        IPage<DwfForm> pageList = dwfFormService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 查询所有
     *
     * @param dwfForm
     * @return
     */
    @AutoLog(value = "动态表单管理-查询所有")
    @GetMapping(value = "/listAll")
    public Result<?> list(DwfForm dwfForm) {
        LambdaQueryWrapper<DwfForm> queryWrapper = new LambdaQueryWrapper<DwfForm>();
        queryWrapper.eq(DwfForm::getTypeId, dwfForm.getTypeId());
        queryWrapper.orderByAsc(DwfForm::getSeq);
        List<DwfForm> list = dwfFormMapper.selectList(queryWrapper);
        return Result.OK(list);
    }

    /**
     * 添加新数据并保存到数据库
     *
     * @param dwfForm
     * @return
     */
    @PostMapping(value = "/add")
    @Operation(summary = "新增动态表单")
    public Result<DwfForm> add(@RequestBody DwfForm dwfForm) {
        Result<DwfForm> result = new Result<DwfForm>();
        try {
            dwfFormService.save(dwfForm);
            result.success("添加成功！");
        } catch (Exception e) {
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 编辑数据
     *
     * @param dwfForm
     * @return
     */
    @PostMapping(value = "/edit")
    @Operation(summary = "编辑动态表单")
    public Result<DwfForm> edit(@RequestBody DwfForm dwfForm) {
        Result<DwfForm> result = new Result<DwfForm>();
        DwfForm dwfFormEntity = dwfFormService.getById(dwfForm.getId());
        if (dwfFormEntity == null) {
            result.error500("未找到对应实体");
        } else {
            dwfForm.setJsonSchema(dwfFormEntity.getJsonSchema());
            dwfFormService.updateById(dwfForm);
            result.success("修改成功!");
        }
        return result;
    }

    /**
     * 保存JsonSchema
     *
     * @param dwfForm
     * @return
     */
    @PostMapping(value = "/save")
    @Operation(summary = "保存JsonSchema数据")
    public Result<DwfForm> save(@RequestBody DwfForm dwfForm) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Result<DwfForm> result = new Result<DwfForm>();
        DwfForm dwfFormEntity = dwfFormService.getById(dwfForm.getId());
        if (dwfFormEntity == null) {
            result.error500("未找到对应实体");
        } else {
            dwfFormEntity.setJsonSchema(dwfForm.getJsonSchema());
            dwfFormService.updateById(dwfFormEntity);
            result.success("保存成功!");
        }
        return result;
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<DwfForm> result = new Result<DwfForm>();
        DwfForm dwfForm = dwfFormService.getById(id);
        if (dwfForm == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(dwfForm);
            result.setSuccess(true);
        }
        return result;
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "动态表单类型管理-通过id删除")
    @DeleteMapping(value = "/delete")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        dwfFormService.removeById(id);
        return Result.OK("删除成功!");
    }


    @GetMapping("/listAuth")
    @Operation(summary = "查询角色权限内的业务表单列表")
    public Result<List<DwfType>> listAuth() {
        Result<List<DwfType>> result = new Result<List<DwfType>>();
        LambdaQueryWrapper<DwfType> typeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        typeLambdaQueryWrapper.orderByAsc(DwfType::getSeq);
        List<DwfType> typeList = dwfTypeService.list(typeLambdaQueryWrapper);
        for (DwfType type : typeList) {
            LambdaQueryWrapper<DwfForm> formLambdaQueryWrapper = new LambdaQueryWrapper<>();
            formLambdaQueryWrapper.eq(DwfForm::getTypeId, type.getId());
            List<DwfForm> formList = dwfFormService.list(formLambdaQueryWrapper);
            type.setFormList(formList);
        }
        result.setSuccess(true);
        result.setResult(typeList);
        return result;
    }

    /**
     * 我的工单
     *
     * @param workOrder
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "OA申请-我的工单")
    @GetMapping(value = "/queryMyOrder")
    public Result<?> queryMyOrder(WorkOrder workOrder,
                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                  HttpServletRequest req) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        workOrder.setStartUserId(sysUser.getId());
        Page<WorkOrder> page = new Page<WorkOrder>(pageNo, pageSize);
        IPage<WorkOrder> pageList = dwfFormMapper.queryWorkOrder(page, workOrder);
        return Result.OK(pageList);
    }

    /**
     * 待办工单
     *
     * @param taskOrder
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "OA申请-我的工单")
    @GetMapping(value = "/queryTaskOrder")
    public Result<?> queryTaskOrder(TaskOrder taskOrder,
                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                    HttpServletRequest req) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        taskOrder.setStartUserId(sysUser.getId());
        Page<TaskOrder> page = new Page<TaskOrder>(pageNo, pageSize);
        IPage<TaskOrder> pageList = dwfFormMapper.queryTaskOrder(page, taskOrder);
        return Result.OK(pageList);
    }

    /**
     * 经办工单
     *
     * @param workOrder
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "OA申请-经办工单")
    @GetMapping(value = "/queryHandlerOrder")
    public Result<?> queryHandlerOrder(WorkOrder workOrder,
                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                       HttpServletRequest req) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        workOrder.setStartUserId(sysUser.getId());
        Page<WorkOrder> page = new Page<WorkOrder>(pageNo, pageSize);
        IPage<WorkOrder> pageList = dwfFormMapper.queryHandleOrder(page, workOrder);
        return Result.OK(pageList);
    }
}
