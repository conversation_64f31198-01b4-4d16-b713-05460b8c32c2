package org.jeecg.modules.camunda.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ActHiProcinst implements Serializable {
    private String id;
    private String procInstId;
    private String businessKey;
    private String procDefKey;
    private String procDefName;
    private String procDefId;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    private Date removalTime;
    private Long duration;
    private String startUserId;
    private String startUserName;
    private String startActId;
    private String endActId;
    private String superProcessInstanceId;
    private String rootProcInstId;
    private String superCaseInstanceId;
    private String caseInstId;
    private String deleteReason;
    private String tenantId;
    private String state;
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}