package org.jeecg.modules.camunda.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.shiro.SecurityUtils;
import org.camunda.bpm.engine.*;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.history.HistoricTaskInstance;
import org.camunda.bpm.engine.history.UserOperationLogEntry;
import org.camunda.bpm.engine.impl.RepositoryServiceImpl;
import org.camunda.bpm.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.camunda.bpm.engine.impl.pvm.process.ActivityImpl;
import org.camunda.bpm.engine.repository.Deployment;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.runtime.ProcessInstanceModificationBuilder;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.UserTask;
import org.camunda.bpm.model.xml.instance.DomElement;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.camunda.emuns.*;
import org.jeecg.modules.camunda.entity.ActApModel;
import org.jeecg.modules.camunda.entity.ActApOpLog;
import org.jeecg.modules.camunda.mapper.ActApOpLogMapper;
import org.jeecg.modules.camunda.mapper.ActivityMapper;
import org.jeecg.modules.camunda.model.ActHiProcinst;
import org.jeecg.modules.camunda.model.ActRuTask;
import org.jeecg.modules.camunda.model.TaskModel;
import org.jeecg.modules.camunda.service.ActApModelService;
import org.jeecg.modules.camunda.service.ActApOpLogService;
import org.jeecg.modules.camunda.service.ActivityService;
import org.jeecg.modules.camunda.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/act/manage")
public class ActivityController {
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private ActivityService activityService;
    @Autowired
    private ActApModelService actApModelService;
    @Autowired
    private ActApOpLogService actApOpLogService;
    @Autowired
    private ActApOpLogMapper actApOpLogMapper;
    @Resource
    private ActivityMapper activityMapper;

    /**
     * 自定义部署方式: 字符串方式部署
     */
    @AutoLog(value = "camunda接口-自定义部署方式")
    @PostMapping(value = "/deploy")
    public Result<?> deploy(@RequestBody ApiVo apiVo) {
        ActApModel model = actApModelService.getById(apiVo.getModelId());
        //字符串
        Deployment deployment = repositoryService
                .createDeployment()
                .addString(model.getModelKey() + ".bpmn", apiVo.getModelContent())
                .name(model.getModelKey())
                .source("web")
                .deploy();
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionKey(model.getModelKey()).latestVersion().singleResult();
        //设置新版本
        model.setModelContent(apiVo.getModelContent());
        model.setVersion(Double.valueOf(processDefinition.getVersion()));
        model.setDeployState(DeployType.DEPLOY.getName());
        actApModelService.saveOrUpdate(model);
        return Result.OK(model);
    }

    /**
     * 流程启动
     *
     * @return
     */
    @AutoLog(value = "camunda接口-流程启动")
    @PostMapping("/start")
    public Result<JSONObject> start(@RequestBody ApiVo apiVo) throws IOException {
        Result<JSONObject> result = new Result<JSONObject>();
        activityService.startProcessInstanceByKey(apiVo.getProcessKey(), apiVo.getBusinessKey(), result);
        return result;
    }

    /**
     * 流程启动并直接提交申请
     *
     * @return
     */
    @AutoLog(value = "camunda接口-流程启动并直接提交申请")
    @PostMapping("/startAndComplete")
    public Result<JSONObject> startAndComplete(@RequestBody ApiVo apiVo) throws IOException {
        Result<JSONObject> result = activityService.startAndComplete(apiVo);
        return result;
    }

    /**
     * 完成待办
     *
     * @param apiVo
     * @return
     */
    @AutoLog(value = "camunda接口-完成待办")
    @PostMapping("/complete")
    public Result<?> complete(@RequestBody ApiVo apiVo) {
        activityService.complete(apiVo);
        return Result.OK("操作成功！");
    }

    /**
     * 驳回后的提交
     *
     * @return
     */
    @PostMapping("/submit")
    @AutoLog(value = "camunda接口-驳回后的提交")
    public Result<?> submit(@RequestBody ApiVo apiVo) {
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        TaskService taskService = processEngine.getTaskService();
        Task task = taskService.createTaskQuery().taskId(apiVo.getTaskId()).singleResult();
        String procInstId = task.getProcessInstanceId();
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(procInstId)
                .singleResult();
        //如果是表单流程
        Map<String, Object> variables = runtimeService.getVariables(procInstId);
        String jsonTableStr = (String) variables.get(VariablesType.PROC_TABLE.getName());
        TableVo tableVo = JSON.parseObject(jsonTableStr, TableVo.class);
        if (tableVo.getIsDynamic() != null && tableVo.getIsDynamic().equals(BooleanType.Y.getName())) {
            //退回改为B
            String updateSql = "update " + tableVo.getTableName() + " set " + tableVo.getStateCode() + " ='C' where " + tableVo.getTableKey() + " ='" + processInstance.getBusinessKey() + "'";
            activityMapper.updateBySql(updateSql);
        }
        //如果带参数
        if (apiVo.getVariables() != null && apiVo.getVariables().size() > 0) {
            apiVo.getVariables().stream().forEach(variable -> runtimeService.setVariable(task.getExecutionId(), variable.getKey(), variable.getValue()));
        }
        //操作日志保存
        ActApOpLog opLog = new ActApOpLog();
        opLog.setProcInstId(procInstId);
        opLog.setTaskId(task.getId());
        opLog.setTaskName(task.getName());
        opLog.setOpType(OperateType.SUBMIT.getName());
        opLog.setOpDesc("提交");
        opLog.setStartDate(task.getCreateTime());
        actApOpLogService.addOpLog(opLog);
        //提交
        taskService.complete(task.getId());
        return Result.OK("操作成功！");
    }

    /**
     * 任务驳回到起点
     *
     * @param apiVo
     * @return
     */
    @AutoLog(value = "camunda接口-任务驳回到起点")
    @PostMapping("/reject")
    public Result<?> reject(@RequestBody ApiVo apiVo) {
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        HistoryService historyService = processEngine.getHistoryService();
        TaskService taskService = processEngine.getTaskService();
        Task activeTask = taskService.createTaskQuery().taskId(apiVo.getTaskId()).singleResult();
        String processInstancesId = activeTask.getProcessInstanceId();
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstancesId)
                .singleResult();

        //日志信息
        ActApOpLog opLog = new ActApOpLog();
        opLog.setProcInstId(processInstancesId);
        opLog.setTaskId(activeTask.getId());
        opLog.setTaskName(activeTask.getName());
        opLog.setOpType(apiVo.getOpType());
        opLog.setOpDesc(apiVo.getOpDesc());
        opLog.setOpIdea(apiVo.getComment());
        opLog.setStartDate(activeTask.getCreateTime());
        //任务节点处理

        HistoricTaskInstance taskInstance = historyService.createHistoricTaskInstanceQuery()
                .taskId(activeTask.getId())
                .singleResult();
        // 获取流程定义
        ProcessDefinitionEntity processDefinitionEntity =
                (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService).getDeployedProcessDefinition(taskInstance.getProcessDefinitionId());
        // 获取当前活动
        ActivityImpl currentActivity = processDefinitionEntity.findActivity(taskInstance.getTaskDefinitionKey());
        // 获取起始活动
        List<HistoricActivityInstance> historicActivityInstances = historyService.createHistoricActivityInstanceQuery()
                .activityType("userTask")
                .processInstanceId(processInstancesId)
                .finished()
                .orderByHistoricActivityInstanceEndTime()
                .asc()
                .list();
        if (historicActivityInstances.size() == 0) {
            return Result.error("任务无法驳回！");
        }
        ActivityImpl lastActivity = processDefinitionEntity.findActivity(historicActivityInstances.get(0).getActivityId());
        ProcessInstanceModificationBuilder processInstanceModificationBuilder = runtimeService.createProcessInstanceModification(processInstancesId);
        //关闭当前任务
        processInstanceModificationBuilder.cancelAllForActivity(currentActivity.getActivityId());
        UserTask userTask = activityService.getUerTask(processInstancesId, lastActivity.getActivityId());
        List<DomElement> multiElemList = userTask.getDomElement().getChildElements().stream().
                filter(node -> node.getLocalName().equals(DomType.MULTI.getName())).collect(Collectors.toList());
        //获取跳转节点的执行候选人
        Map<String, Object> variables = runtimeService.getVariables(processInstancesId);

        //如果是表单流程
        String jsonTableStr = (String) variables.get(VariablesType.PROC_TABLE.getName());
        TableVo tableVo = JSON.parseObject(jsonTableStr, TableVo.class);
        if (tableVo.getIsDynamic() != null && tableVo.getIsDynamic().equals(BooleanType.Y.getName())) {
            //退回改为B
            String updateSql = "update " + tableVo.getTableName() + " set " + tableVo.getStateCode() + " ='B' where " + tableVo.getTableKey() + " ='" + processInstance.getBusinessKey() + "'";
            activityMapper.updateBySql(updateSql);
        }
        if (multiElemList.size() > 0) {
            //多实例
            List<String> users = (List<String>) variables.get(lastActivity.getActivityId() + "_" + TaskUserType.LIST.getName());
            for (String userId : users) {
                processInstanceModificationBuilder
                        .startBeforeActivity(lastActivity.getActivityId())
                        .setVariableLocal(lastActivity.getActivityId() + "_" + TaskUserType.SINGLE.getName(), userId);
            }
        } else {
            //单实例
            String userId = (String) variables.get(lastActivity.getActivityId() + "_" + TaskUserType.SINGLE.getName());
            processInstanceModificationBuilder
                    .startBeforeActivity(lastActivity.getActivityId())
                    .setVariableLocal(lastActivity.getActivityId() + "_" + TaskUserType.SINGLE.getName(), userId);
        }
        //修改执行
        processInstanceModificationBuilder.execute();

        //保存日志
        actApOpLogService.addOpLog(opLog);
        return Result.OK("操作成功！");
    }

    /**
     * 任务退回至指定节点
     *
     * @param apiVo
     * @return
     */
    @AutoLog(value = "camunda接口-任务驳回到起点")
    @PostMapping("/backAny")
    public Result<?> backAny(@RequestBody ApiVo apiVo) {
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        HistoryService historyService = processEngine.getHistoryService();
        TaskService taskService = processEngine.getTaskService();
        Task activeTask = taskService.createTaskQuery().taskId(apiVo.getTaskId()).singleResult();
        String processInstancesId = activeTask.getProcessInstanceId();

        //日志信息
        ActApOpLog opLog = new ActApOpLog();
        opLog.setProcInstId(processInstancesId);
        opLog.setTaskId(activeTask.getId());
        opLog.setTaskName(activeTask.getName());
        opLog.setOpType(apiVo.getOpType());
        opLog.setOpDesc(apiVo.getOpDesc());
        opLog.setOpIdea(apiVo.getComment());
        opLog.setStartDate(activeTask.getCreateTime());

        //任务节点处理

        HistoricTaskInstance taskInstance = historyService.createHistoricTaskInstanceQuery()
                .taskId(activeTask.getId())
                .singleResult();
        // 获取流程定义
        ProcessDefinitionEntity processDefinitionEntity =
                (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService).getDeployedProcessDefinition(taskInstance.getProcessDefinitionId());
        // 获取当前活动
        ActivityImpl currentActivity = processDefinitionEntity.findActivity(taskInstance.getTaskDefinitionKey());
        // 获取起始活动
        List<HistoricActivityInstance> historicActivityInstances = historyService.createHistoricActivityInstanceQuery()
                .activityType("userTask")
                .processInstanceId(processInstancesId)
                .finished()
                .orderByHistoricActivityInstanceEndTime()
                .asc()
                .list();
        if (historicActivityInstances.size() == 0) {
            return Result.error("任务无法驳回！");
        }
        ActivityImpl lastActivity = processDefinitionEntity.findActivity(apiVo.getBackTaskKey());
        ProcessInstanceModificationBuilder processInstanceModificationBuilder = runtimeService.createProcessInstanceModification(processInstancesId);
        //关闭当前任务
        processInstanceModificationBuilder.cancelAllForActivity(currentActivity.getActivityId());
        UserTask userTask = activityService.getUerTask(processInstancesId, lastActivity.getActivityId());
        //获取跳转节点的执行候选人
        Map<String, Object> variables = runtimeService.getVariables(processInstancesId);
        if (userTask.getLoopCharacteristics() != null) {
            //多实例
            List<String> users = (List<String>) variables.get(lastActivity.getActivityId() + "_" + TaskUserType.LIST.getName());
            for (String userId : users) {
                processInstanceModificationBuilder
                        .startBeforeActivity(lastActivity.getActivityId())
                        .setVariableLocal(lastActivity.getActivityId() + "_" + TaskUserType.SINGLE.getName(), userId);
            }
        } else {
            //单实例
            String userId = (String) variables.get(lastActivity.getActivityId() + "_" + TaskUserType.SINGLE.getName());
            processInstanceModificationBuilder
                    .startBeforeActivity(lastActivity.getActivityId())
                    .setVariableLocal(lastActivity.getActivityId() + "_" + TaskUserType.SINGLE.getName(), userId);
        }
        //修改执行
        processInstanceModificationBuilder.execute();
        //保存日志
        actApOpLogService.addOpLog(opLog);
        return Result.OK("操作成功！");
    }

    /**
     * 流程撤回
     *
     * @param apiVo
     * @return
     */
    @AutoLog(value = "camunda接口-流程撤回")
    @PostMapping("/recall")
    public Result<?> recall(@RequestBody ApiVo apiVo) {
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        HistoryService historyService = processEngine.getHistoryService();
        String processInstancesId = apiVo.getProcInstId();
        TaskService taskService = processEngine.getTaskService();
        Task activeTask = taskService.createTaskQuery().processInstanceId(processInstancesId).active().singleResult();
        //日志信息
        ActApOpLog opLog = new ActApOpLog();
        opLog.setProcInstId(processInstancesId);
        opLog.setTaskId(activeTask.getId());
        opLog.setTaskName(activeTask.getName());
        opLog.setOpType(apiVo.getOpType());
        opLog.setOpDesc(apiVo.getOpDesc());
        opLog.setOpIdea(apiVo.getComment());
        opLog.setStartDate(activeTask.getCreateTime());
        //任务节点处理

        HistoricTaskInstance taskInstance = historyService.createHistoricTaskInstanceQuery()
                .taskId(activeTask.getId())
                .singleResult();
        // 获取流程定义
        ProcessDefinitionEntity processDefinitionEntity =
                (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService).getDeployedProcessDefinition(taskInstance.getProcessDefinitionId());
        // 获取当前活动
        ActivityImpl currentActivity = processDefinitionEntity.findActivity(taskInstance.getTaskDefinitionKey());
        // 获取起始活动
        List<HistoricActivityInstance> historicActivityInstances = historyService.createHistoricActivityInstanceQuery()
                .activityType("userTask")
                .processInstanceId(processInstancesId)
                .finished()
                .orderByHistoricActivityInstanceEndTime()
                .asc()
                .list();
        if (historicActivityInstances.size() == 0) {
            return Result.error("任务无法驳回！");
        }
        ActivityImpl lastActivity = processDefinitionEntity.findActivity(historicActivityInstances.get(0).getActivityId());
        ProcessInstanceModificationBuilder processInstanceModificationBuilder = runtimeService.createProcessInstanceModification(processInstancesId);
        //关闭当前任务
        processInstanceModificationBuilder.cancelAllForActivity(currentActivity.getActivityId());
        UserTask userTask = activityService.getUerTask(processInstancesId, lastActivity.getActivityId());
        List<DomElement> multiElemList = userTask.getDomElement().getChildElements().stream().
                filter(node -> node.getLocalName().equals(DomType.MULTI.getName())).collect(Collectors.toList());
        //获取跳转节点的执行候选人
        Map<String, Object> variables = runtimeService.getVariables(processInstancesId);
        if (multiElemList.size() > 0) {
            //多实例
            List<String> users = (List<String>) variables.get(lastActivity.getActivityId() + "_" + TaskUserType.LIST.getName());
            for (String userId : users) {
                processInstanceModificationBuilder
                        .startBeforeActivity(lastActivity.getActivityId())
                        .setVariableLocal(lastActivity.getActivityId() + "_" + TaskUserType.SINGLE.getName(), userId);
            }
        } else {
            //单实例
            String userId = (String) variables.get(lastActivity.getActivityId() + "_" + TaskUserType.SINGLE.getName());
            processInstanceModificationBuilder
                    .startBeforeActivity(lastActivity.getActivityId())
                    .setVariableLocal(lastActivity.getActivityId() + "_" + TaskUserType.SINGLE.getName(), userId);
        }
        //修改执行
        processInstanceModificationBuilder.execute();
        //保存日志
        actApOpLogService.addOpLog(opLog);
        return Result.OK("操作成功！");
    }

    /**
     * 任务转办
     *
     * @param apiVo
     * @return
     */
    @AutoLog(value = "camunda接口-任务转办")
    @PostMapping("/transfer")
    public Result<?> transfer(@RequestBody ApiVo apiVo) {
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        TaskService taskService = processEngine.getTaskService();
        //查询当前实例任务节点
        Task currTask = taskService.createTaskQuery().taskId(apiVo.getTaskId()).singleResult();
        //判断当前实例任务节点是否已存在转办的候选人
        Task targetTask = taskService.createTaskQuery().
                taskDefinitionKey(currTask.getTaskDefinitionKey()).
                processInstanceId(currTask.getProcessInstanceId()).
                taskAssignee(apiVo.getUser().getId()).
                singleResult();
        if (targetTask != null) {
            return Result.error("执行候选人已存在！");
        } else {//获取登录用户信息
            taskService.setAssignee(apiVo.getTaskId(), apiVo.getUser().getId());
            OpLogVo opLog = new OpLogVo();
            opLog.setType(OperateType.TRANSFER.getName());
            opLog.setTaskKey(currTask.getTaskDefinitionKey());
            opLog.setTaskName(currTask.getName());
            opLog.setHandleDesc("任务转办至【" + apiVo.getUser().getRealName() + "】");
            activityService.addOpLog(runtimeService.createProcessInstanceModification(currTask.getProcessInstanceId()), opLog);
            return Result.OK("转办成功！");
        }
    }

    /**
     * 加签
     *
     * @param apiVo
     * @return
     */
    @AutoLog(value = "camunda接口-加签")
    @PostMapping("/addAssignees")
    public Result<?> addAssignees(@RequestBody ApiVo apiVo) {
        Map<String, Object> variables = runtimeService.getVariables(apiVo.getProcInstId());
        Object obj = variables.get(apiVo.getTaskKey() + "_" + TaskUserType.LIST.getName());
        if (obj == null) {
            return Result.error("当前任务为单人审核类型，不允许加签！");
        }
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        TaskService taskService = processEngine.getTaskService();
        ProcessInstanceModificationBuilder processInstanceModificationBuilder = runtimeService.createProcessInstanceModification(apiVo.getProcInstId());
        StringBuffer addNameStr = new StringBuffer();
        for (TaskUserVo userVo : apiVo.getUserList()) {
            Task targetTask = taskService.createTaskQuery().
                    taskDefinitionKey(apiVo.getTaskKey()).
                    processInstanceId(apiVo.getProcInstId()).
                    taskAssignee(userVo.getId()).
                    singleResult();
            if (targetTask == null) {
                addNameStr.append("【" + userVo.getRealName() + "】");
                processInstanceModificationBuilder
                        .startBeforeActivity(apiVo.getTaskKey())
                        .setVariableLocal(apiVo.getTaskKey() + "_" + TaskUserType.SINGLE.getName(), userVo.getId());
            }
        }
        UserTask userTask = activityService.getUerTask(apiVo.getProcInstId(), apiVo.getTaskKey());
        OpLogVo opLog = new OpLogVo();
        opLog.setType(OperateType.ADD.getName());
        opLog.setTaskKey(apiVo.getTaskKey());
        opLog.setTaskName(userTask.getName());
        opLog.setHandleDesc("添加候选执行人" + addNameStr.toString());
        activityService.addOpLog(processInstanceModificationBuilder, opLog);
        return Result.OK("加签成功！");
    }

    /**
     * 减签
     *
     * @param apiVo
     * @return
     */
    @AutoLog(value = "camunda接口-减签")
    @PostMapping("/reduceAssignees")
    public Result<?> reduceAssignees(@RequestBody ApiVo apiVo) {
        Map<String, Object> variables = runtimeService.getVariables(apiVo.getProcInstId());
        Object obj = variables.get(apiVo.getTaskKey() + "_" + TaskUserType.LIST.getName());
        if (obj == null) {
            return Result.error("当前任务为单人审核类型，不允许减签！");
        }
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        TaskService taskService = processEngine.getTaskService();
        StringBuffer addNameStr = new StringBuffer();
        for (TaskUserVo userVo : apiVo.getUserList()) {
            Task targetTask = taskService.createTaskQuery().
                    taskDefinitionKey(apiVo.getTaskKey()).
                    processInstanceId(apiVo.getProcInstId()).
                    taskAssignee(userVo.getId()).
                    singleResult();
            if (targetTask != null) {
                addNameStr.append("【" + userVo.getRealName() + "】");
                activityService.deleteTask(targetTask);
            }
        }
        UserTask userTask = activityService.getUerTask(apiVo.getProcInstId(), apiVo.getTaskKey());
        OpLogVo opLog = new OpLogVo();
        opLog.setType(OperateType.REDUCE.getName());
        opLog.setTaskKey(apiVo.getTaskKey());
        opLog.setTaskName(userTask.getName());
        opLog.setHandleDesc("删除候选执行人" + addNameStr.toString());
        //日志
        activityService.addOpLog(runtimeService.createProcessInstanceModification(apiVo.getProcInstId()), opLog);
        return Result.OK("减签成功！");
    }

    /**
     * 任务节点跳转
     *
     * @param apiVo
     * @return
     */
    @AutoLog(value = "camunda接口-任务节点跳转")
    @PostMapping("/jumpNode")
    public Result<?> jumpNode(@RequestBody ApiVo apiVo) throws IOException {
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        TaskService taskService = processEngine.getTaskService();
        Task currentTask = taskService.createTaskQuery().taskId(apiVo.getTaskId()).singleResult();
        FlowNode flowNode = activityService.getFlowNode(apiVo.getProcInstId(), apiVo.getTargetKey());
        //操作日志保存
        ActApOpLog opLog = new ActApOpLog();
        opLog.setProcInstId(apiVo.getProcInstId());
        opLog.setTaskId(currentTask.getId());
        opLog.setTaskName(currentTask.getName());
        opLog.setOpType(OperateType.JUMP.getName());
        opLog.setOpDesc("跳转至任务节点【" + flowNode.getName() + "】");
        opLog.setOpIdea(apiVo.getComment());
        opLog.setStartDate(currentTask.getCreateTime());
        actApOpLogService.addOpLog(opLog);
        //开始处理节点
        ProcessInstanceModificationBuilder processInstanceModificationBuilder = runtimeService.createProcessInstanceModification(apiVo.getProcInstId());
        //取消已活动的任务节点
        Set<String> activityIdSet = new HashSet<>();
        taskService.createTaskQuery().processInstanceId(apiVo.getProcInstId()).active().list().forEach(taskQuery -> {
            String activityId = taskQuery.getTaskDefinitionKey();
            if (activityIdSet.add(activityId)) {
                processInstanceModificationBuilder.cancelAllForActivity(activityId);
            }
        });
        if (flowNode.getElementType().getTypeName().equals(DomType.USER_TASK.getName())) {
            UserTask userTask = activityService.getUerTask(apiVo.getProcInstId(), apiVo.getTargetKey());
            List<DomElement> multiElemList = userTask.getDomElement().getChildElements().stream().
                    filter(node -> node.getLocalName().equals(DomType.MULTI.getName())).collect(Collectors.toList());
            //获取跳转节点的执行候选人
            Map<String, Object> variables = runtimeService.getVariables(apiVo.getProcInstId());
            if (multiElemList.size() > 0) {
                //多实例
                List<String> users = (List<String>) variables.get(apiVo.getTargetKey() + "_" + TaskUserType.LIST.getName());
                for (String userId : users) {
                    processInstanceModificationBuilder
                            .startBeforeActivity(apiVo.getTargetKey())
                            .setVariableLocal(apiVo.getTargetKey() + "_" + TaskUserType.SINGLE.getName(), userId);
                }
            } else {
                //单实例
                String userId = (String) variables.get(apiVo.getTargetKey() + "_" + TaskUserType.SINGLE.getName());
                processInstanceModificationBuilder
                        .startBeforeActivity(apiVo.getTargetKey())
                        .setVariableLocal(apiVo.getTargetKey() + "_" + TaskUserType.SINGLE.getName(), userId);
            }
        } else {
            processInstanceModificationBuilder.startBeforeActivity(apiVo.getTargetKey());
        }
        processInstanceModificationBuilder.execute();
        return Result.OK("跳转成功！");
    }

    /**
     * 获取操作日志
     *
     * @param procInstId
     * @return
     */
    @AutoLog(value = "camunda接口-获取操作日志")
    @GetMapping("/getOperationLog")
    public Result<?> getOperationLog(@RequestParam(name = "procInstId", required = true) String procInstId) {
        List<UserOperationLogEntry> logList = historyService.createUserOperationLogQuery()
                .processInstanceId(procInstId)
                .list();
        return Result.OK(logList);
    }

    /**
     * 流程实例挂起
     *
     * @param apiVo
     * @return
     */
    @AutoLog(value = "camunda接口-流程实例挂起")
    @PostMapping("/suspend")
    public Result<?> suspend(@RequestBody ApiVo apiVo) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(apiVo.getProcInstId())
                .singleResult();
        if (processInstance != null) {
            runtimeService.suspendProcessInstanceById(apiVo.getProcInstId());
            return Result.OK("操作成功！");
        } else {
            return Result.error("当前流程实例不存在！");
        }


    }

    /**
     * 流程实例激活
     *
     * @param apiVo
     * @return
     */
    @AutoLog(value = "camunda接口-流程实例激活")
    @PostMapping("/activate")
    public Result<?> activate(@RequestBody ApiVo apiVo) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(apiVo.getProcInstId())
                .singleResult();
        if (processInstance != null) {
            runtimeService.activateProcessInstanceById(apiVo.getProcInstId());
            return Result.OK("操作成功！");
        } else {
            return Result.error("当前流程实例不存在！");
        }
    }

    /**
     * 流程实例删除
     *
     * @param apiVo
     * @return
     */
    @AutoLog(value = "camunda接口-流程实例删除")
    @PostMapping("/delete")
    public Result<?> delete(@RequestBody ApiVo apiVo) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(apiVo.getProcInstId())
                .singleResult();
        if (processInstance != null) {
            runtimeService.deleteProcessInstance(apiVo.getProcInstId(), apiVo.getComment());
            return Result.OK("操作成功！");
        } else {
            return Result.error("当前流程实例不存在！");
        }
    }

    /**
     * 我的发起
     *
     * @param actHiProcinst
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "camunda接口-我的发起")
    @GetMapping(value = "/initiationList")
    public Result<?> initiationList(ActHiProcinst actHiProcinst,
                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                    HttpServletRequest req) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        actHiProcinst.setStartUserId(sysUser.getId());
        Page<ActHiProcinst> page = new Page<ActHiProcinst>(pageNo, pageSize);
        IPage<ActHiProcinst> pageList = actApOpLogMapper.queryHisProcinst(page, actHiProcinst);
        return Result.OK(pageList);
    }

    /**
     * 我的待办
     *
     * @param paramTask
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "camunda接口-我的待办")
    @GetMapping(value = "/todoList")
    public Result<?> todoList(ActRuTask paramTask,
                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                              HttpServletRequest req) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        paramTask.setAssignee(sysUser.getId());
        Page<ActRuTask> page = new Page<ActRuTask>(pageNo, pageSize);
        IPage<ActRuTask> pageList = actApOpLogMapper.queryRunTask(page, paramTask);
        return Result.OK(pageList);
    }

    /**
     * 我的经办
     *
     * @param actHiProcinst
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "camunda接口-我的经办")
    @GetMapping(value = "/handleList")
    public Result<?> handleList(ActHiProcinst actHiProcinst,
                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                HttpServletRequest req) {

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        actHiProcinst.setStartUserId(sysUser.getId());
        Page<ActHiProcinst> page = new Page<ActHiProcinst>(pageNo, pageSize);
        IPage<ActHiProcinst> pageList = actApOpLogMapper.queryHandleProcinst(page, actHiProcinst);
        return Result.OK(pageList);
    }

    /**
     * 抄送我的
     *
     * @param actHiProcinst
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "camunda接口-抄送我的")
    @GetMapping(value = "/sendList")
    public Result<?> sendList(ActHiProcinst actHiProcinst,
                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                              HttpServletRequest req) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        actHiProcinst.setStartUserId(sysUser.getId());
        Page<ActHiProcinst> page = new Page<ActHiProcinst>(pageNo, pageSize);
        IPage<ActHiProcinst> pageList = actApOpLogMapper.querySendProcinst(page, actHiProcinst);
        return Result.OK(pageList);
    }

    /**
     * 获取实例对应的bpmn图
     *
     * @param actHiProcinst
     * @return
     */
    @AutoLog(value = "camunda接口-获取实例对应的bpmn图")
    @GetMapping(value = "/getBpmn")
    public Result<?> getBpmn(ActHiProcinst actHiProcinst) throws IOException {
        //历史流程实例
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(actHiProcinst.getProcInstId()).singleResult();
        // 获取流程定义对象
        ProcessDefinition processDefinition = repositoryService.getProcessDefinition(historicProcessInstance.getProcessDefinitionId());
        // 获取 BPMN XML 内容
        InputStream ins = repositoryService.getProcessModel(processDefinition.getId());
        String bpmnXml = IOUtils.toString(ins, StandardCharsets.UTF_8);
        ins.close();
        List<TaskModel> taskModelList = activityService.getTaskListByProcInst(actHiProcinst.getProcInstId());
        HashMap<String, Object> map = new HashMap<String, Object>(16);
        map.put("bpmnXml", bpmnXml);
        map.put("taskKeyArr", taskModelList);
        return Result.OK(map);
    }

    /**
     * 根据流程key获取实例对应的bpmn图
     *
     * @param actHiProcinst
     * @return
     */
    @AutoLog(value = "camunda接口-根据流程key获取实例对应的bpmn图")
    @GetMapping(value = "/getBpmnByKey")
    public Result<?> getBpmnByKey(ActHiProcinst actHiProcinst) throws IOException {
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(actHiProcinst.getProcDefKey()).latestVersion().singleResult();
        String processDefinitionId = processDefinition.getId();
        InputStream ins = ProcessEngines
                .getDefaultProcessEngine()
                .getRepositoryService()
                .getProcessModel(processDefinitionId);
        String bpmnXml = IOUtils.toString(ins, StandardCharsets.UTF_8);
        ins.close();
        HashMap<String, Object> map = new HashMap<String, Object>(16);
        map.put("bpmnXml", bpmnXml);
        return Result.OK(map);
    }

    /**
     * 获取用户节点所有按钮操作权限
     *
     * @param apiVo
     * @return
     */
    @AutoLog(value = "camunda接口-获取用户节点所有按钮操作权限")
    @GetMapping(value = "/getTaskProps")
    public Result<?> getTaskProps(ApiVo apiVo) {
        //获取跳转节点的执行候选人
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        TaskService taskService = processEngine.getTaskService();
        Task currTask = taskService.createTaskQuery().taskId(apiVo.getTaskId()).singleResult();
        Map<String, Object> variables = runtimeService.getVariables(apiVo.getProcInstId());
        String jsonButtons = (String) variables.get(currTask.getTaskDefinitionKey() + "_btn_auth");
        ButtonVo btnVo = JSON.parseObject(jsonButtons, ButtonVo.class);
        List<String> formVarList = new ArrayList<String>();
        //如果有参
        String formVariable = currTask.getTaskDefinitionKey() + "_form_var";
        if (variables.get(formVariable) != null) {
            String vars = (String) variables.get(formVariable);
            formVarList = Arrays.asList(vars.split(","));
        }
        HashMap<String, Object> map = new HashMap<String, Object>(16);
        map.put("buttonAuth", btnVo);
        map.put("formVars", formVarList);
        return Result.OK(map);
    }

    /**
     * 根据任务id获取之前的所有userTask
     *
     * @param apiVo
     * @return
     */
    @AutoLog(value = "camunda接口-根据任务id获取之前的所有userTask")
    @GetMapping(value = "/preTasks")
    public Result<?> preTasks(ApiVo apiVo) throws IOException {
        //获取当前节点
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        TaskService taskService = processEngine.getTaskService();
        Task currTask = taskService.createTaskQuery().taskId(apiVo.getTaskId()).singleResult();
        //获取流程模板
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(currTask.getProcessInstanceId())
                .singleResult();
        InputStream processModel = ProcessEngines
                .getDefaultProcessEngine()
                .getRepositoryService()
                .getProcessModel(processInstance.getProcessDefinitionId());
        BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(processModel);
        processModel.close();
        //获取当前节点之前的所有userTask
        List<TaskModel> preTaskList = new ArrayList<TaskModel>();
        List<FlowNode> predFlowList = new ArrayList<>();
        // 记录已经访问过的节点
        List<FlowNode> visitedNodes = new ArrayList<>();
        FlowNode targetNode = bpmnModelInstance.getModelElementById(currTask.getTaskDefinitionKey());
        activityService.findPreFlowList(targetNode, predFlowList, visitedNodes);
        for (FlowNode flow : predFlowList) {
            if (flow.getElementType().getTypeName().equals(DomType.USER_TASK.getName())) {
                TaskModel taskModel = new TaskModel();
                taskModel.setTaskKey(flow.getId());
                taskModel.setTaskName(flow.getName());
                preTaskList.add(taskModel);
            }
        }
        return Result.OK(preTaskList);
    }

    /**
     * 外部任务完成
     *
     * @param apiVo
     * @return
     */
    @AutoLog(value = "camunda接口-外部任务完成")
    @PostMapping("/externalComplete")
    public Result<?> externalComplete(@RequestBody ApiVo apiVo) {
        activityService.externalComplete(apiVo);
        return Result.OK("成功");
    }
}

