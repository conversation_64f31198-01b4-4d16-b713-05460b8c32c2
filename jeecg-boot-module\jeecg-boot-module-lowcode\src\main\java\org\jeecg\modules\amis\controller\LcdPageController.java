package org.jeecg.modules.amis.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.amis.entity.LcdPage;
import org.jeecg.modules.amis.service.LcdPageService;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 低代码页面管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/lcd/page")
@Tag(name = "低代码页面管理")
public class LcdPageController {

    @Resource
    private LcdPageService lcdPageService;

    /**
     * 分页列表查询
     *
     * @param lcdPage
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */

    @ResponseBody
    @GetMapping("/list")
    @Operation(summary = "分页查询低代码页面")
    public Result<IPage<LcdPage>> queryPageList(LcdPage lcdPage,
                                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        Result<IPage<LcdPage>> result = new Result<IPage<LcdPage>>();
        //默认查询未删除的数据
        lcdPage.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
        QueryWrapper<LcdPage> queryWrapper = QueryGenerator.initQueryWrapper(lcdPage, req.getParameterMap());
        Page<LcdPage> page = new Page<>(pageNo, pageSize);
        IPage<LcdPage> pageList = lcdPageService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加新数据并保存到数据库
     *
     * @param lcdPage
     * @return
     */
    @PostMapping(value = "/add")
    @Operation(summary = "添加模低代码页面")
    public Result<LcdPage> add(@RequestBody LcdPage lcdPage, HttpServletRequest request) {
        Result<LcdPage> result = new Result<LcdPage>();
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            lcdPage.setCreateBy(sysUser.getRealname());
            lcdPage.setCreateTime(new Date());
            lcdPageService.save(lcdPage);
            result.success("添加成功！");
        } catch (Exception e) {
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 编辑数据(不包含JsonSchema)
     *
     * @param lcdPage
     * @return
     */
    @PostMapping(value = "/edit")
    @Operation(summary = "编辑低代码页面")
    public Result<LcdPage> edit(@RequestBody LcdPage lcdPage, HttpServletRequest request) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Result<LcdPage> result = new Result<LcdPage>();
        LcdPage lcdPageEntity = lcdPageService.getById(lcdPage.getId());
        if (lcdPageEntity == null) {
            result.error500("未找到对应实体");
        } else {
            lcdPage.setUpdateBy(sysUser.getRealname());
            lcdPage.setUpdateTime(new Date());
            lcdPage.setJsonSchema(lcdPageEntity.getJsonSchema());
            lcdPageService.updateById(lcdPage);
            result.success("修改成功!");
        }
        return result;
    }

    /**
     * 保存JsonSchema
     *
     * @param lcdPage
     * @return
     */
    @PostMapping(value = "/save")
    @Operation(summary = "保存JsonSchema数据")
    public Result<LcdPage> save(@RequestBody LcdPage lcdPage) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Result<LcdPage> result = new Result<LcdPage>();
        LcdPage lcdPageEntity = lcdPageService.getById(lcdPage.getId());
        if (lcdPageEntity == null) {
            result.error500("未找到对应实体");
        } else {
            lcdPageEntity.setUpdateBy(sysUser.getRealname());
            lcdPageEntity.setUpdateTime(new Date());
            lcdPageEntity.setJsonSchema(lcdPage.getJsonSchema());
            lcdPageService.updateById(lcdPageEntity);
            result.success("保存成功!");
        }
        return result;
    }

    /**
     * 根据id获取对象
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/queryById")
    @Operation(summary = "根据id获取低代码页面")
    public Result<LcdPage> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<LcdPage> result = new Result<LcdPage>();
        LcdPage lcdPage = lcdPageService.getById(id);
        if (lcdPage == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(lcdPage);
            result.setSuccess(true);
        }
        return result;
    }


    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @DeleteMapping(value = "/delete")
    @Operation(summary = "根据id删除模网格人员")
    public Result<LcdPage> delete(@RequestParam(name = "id", required = true) String id) {
        Result<LcdPage> result = new Result<LcdPage>();
        LcdPage lcdPage = lcdPageService.getById(id);
        if (lcdPage == null) {
            return Result.error("未找到对应实体！");
        } else {
            //删除实体对象
            lcdPageService.removeById(id);
            return Result.ok("删除成功！");
        }
    }


    /**
     * 根据token获取用户信息接口
     * 该接口通过token来获取当前登录用户的用户信息。
     *
     * @return 返回包含用户信息的Result对象
     */
    @GetMapping(value = "/queryUserInfo")
    @Operation(summary = "根据token获取用户信息")
    public Result<?> queryUserInfo() {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        return Result.ok(sysUser);
    }
}
