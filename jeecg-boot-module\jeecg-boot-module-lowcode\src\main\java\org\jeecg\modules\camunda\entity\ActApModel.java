package org.jeecg.modules.camunda.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程应用模板
 *
 * <AUTHOR>
 * @TableName ACT_AP_MODEL
 */
@TableName(value = "ACT_AP_MODEL")
@Data
public class ActApModel implements Serializable {
    /**
     * 模板id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 模板标识
     */
    private String modelKey;

    /**
     * 模板名称
     */
    private String modelName;
    /**
     * 模板内容
     */
    private String modelContent;

    /**
     * 应用类型id;ACT_AP_TYPE主键
     */
    private String typeId;

    /**
     * 版本号
     */
    private Double version;

    /**
     * 部署状态
     */
    private String deployState;

    /**
     * 使用状态
     */
    private String useState;

    /**
     * 序号
     */
    private Double seq;
    /**
     * 描述
     */
    private String remark;

    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date creDate;
    /**
     * 图标
     */
    private String icon;
    /**
     * 背景颜色
     */
    private String bgColor;
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}