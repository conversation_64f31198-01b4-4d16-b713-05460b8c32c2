package org.jeecg.modules.camunda.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.camunda.entity.ActApType;
import org.jeecg.modules.camunda.mapper.ActApTypeMapper;
import org.jeecg.modules.camunda.service.ActApTypeService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ActApTypeServiceImpl extends ServiceImpl<ActApTypeMapper, ActApType>
        implements ActApTypeService {

}




