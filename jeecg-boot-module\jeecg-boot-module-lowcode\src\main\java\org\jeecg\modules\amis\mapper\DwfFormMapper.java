package org.jeecg.modules.amis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.jeecg.modules.amis.entity.DwfForm;
import org.jeecg.modules.amis.model.TaskOrder;
import org.jeecg.modules.amis.model.WorkOrder;

/**
 * <AUTHOR>
 * @description 针对表【dwf_form(业务表单)】的数据库操作Mapper
 * @createDate 2025-06-25 14:11:42
 * @Entity org.jeecg.modules.amis.entity.DwfForm
 */
public interface DwfFormMapper extends BaseMapper<DwfForm> {

    /**
     * 我的工单
     *
     * @param page
     * @param workOrder
     * @return
     */
    IPage<WorkOrder> queryWorkOrder(IPage<WorkOrder> page, WorkOrder workOrder);

    /**
     * 待办工单
     *
     * @param page
     * @param taskOrder
     * @return
     */
    IPage<TaskOrder> queryTaskOrder(IPage<TaskOrder> page, TaskOrder taskOrder);

    /**
     * 经办工单
     *
     * @param page
     * @param workOrder
     * @return
     */
    IPage<WorkOrder> queryHandleOrder(IPage<WorkOrder> page, WorkOrder workOrder);

}




