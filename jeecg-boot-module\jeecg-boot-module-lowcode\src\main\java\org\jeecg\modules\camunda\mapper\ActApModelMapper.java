package org.jeecg.modules.camunda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.camunda.entity.ActApModel;
import org.jeecg.modules.camunda.entity.ActApType;
import org.jeecg.modules.camunda.model.UserModel;
import org.jeecg.modules.camunda.vo.RoleVo;
import org.jeecg.modules.camunda.vo.TaskPositionVo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Entity camunda.entity.ActApModel
 */
@Mapper
@Repository
public interface ActApModelMapper extends BaseMapper<ActApModel> {


    /**
     * 待选用户查询
     *
     * @param page
     * @param name
     * @return
     */
    IPage<UserModel> queryUserPage(IPage<UserModel> page, @Param("name") String name);

    /**
     * 已选用户查询
     *
     * @param ids
     * @return
     */
    List<UserModel> queryUserSelect(List<String> ids);

    /**
     * 待选角色查询
     *
     * @param page
     * @param name
     * @return
     */
    IPage<RoleVo> queryRolePage(IPage<RoleVo> page, @Param("name") String name);

    /**
     * 已选用户角色
     *
     * @param ids
     * @return
     */
    List<RoleVo> queryRoleSelect(List<String> ids);

    /**
     * 待选职位查询
     *
     * @param page
     * @param name
     * @return
     */
    IPage<TaskPositionVo> queryPositionListPage(IPage<TaskPositionVo> page, @Param("name") String name);

    /**
     * 已选职位角色
     *
     * @param ids
     * @return
     */
    List<TaskPositionVo> queryPositionSelect(List<String> ids);

    /**
     * 查询权限内的流程业务
     *
     * @param userId
     * @return
     */
    List<ActApType> queryApType(@Param("userId") String userId);

    /**
     * 查询权限内的流程业务
     *
     * @param userId
     * @param typeId
     * @return
     */
    List<ActApModel> queryModelByAuth(@Param("userId") String userId, @Param("typeId") String typeId);
}




